[package]
name = "rusty-rain"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
wasm-bindgen = "0.2"
js-sys = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.6"
serde_json = "1.0"
getrandom = { version = "0.2", features = ["js"] }
rand = "0.8"
chrono = { version = "0.4", features = ["wasm-bindgen"] }

[dependencies.web-sys]
version = "0.3"
features = [
  "console",
  "Document",
  "Element",
  "HtmlCanvasElement",
  "CanvasRenderingContext2d",
  "Window",
  "Navigator",
  "Geolocation",
  "Position",
  "Coordinates",
  "Request",
  "RequestInit",
  "RequestMode",
  "Response",
  "Headers",
]

[profile.release]
opt-level = "s"
lto = true

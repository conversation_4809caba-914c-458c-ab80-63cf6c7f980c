# 🌦️ Rusty Rain - Weather Simulation WASM App

A realistic weather simulation web application built with Rust and WebAssembly. Features dynamic weather effects, celestial bodies (sun/moon), animated landscapes, and real-time weather data integration.

## Features

- **Real Weather Data**: Fetches current weather conditions using your location
- **Dynamic Weather Effects**: Rain, snow, clouds, fog, and thunderstorms
- **Celestial Bodies**: Sun and moon positioning based on time of day
- **Animated Characters**: People, children, elders, and dogs walking around
- **Interactive Conversations**: Characters meet and chat about the weather
- **Animated Elements**: Flying birds, moving clouds, and weather particles
- **Responsive Design**: Works on desktop and mobile devices
- **No API Keys Required**: Uses free Open-Meteo weather API

## Weather Effects

- ☀️ **Clear Sky**: Bright blue sky with sun
- ⛅ **Partly Cloudy**: Moving clouds with sun/moon
- 🌧️ **Rain**: Animated raindrops with varying intensity
- ❄️ **Snow**: Falling snowflakes
- 🌫️ **Fog**: Atmospheric fog effects
- ⛈️ **Thunderstorms**: Rain with lightning flashes
- 🌙 **Night Mode**: Dark sky with moon and stars
- 👥 **Living World**: Characters walking, meeting, and chatting about weather

## Prerequisites

Before building this project, you need to install:

1. **Rust**: Install from [rustup.rs](https://rustup.rs/)
2. **wasm-pack**: Install with `curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh`

## Building and Running

### Option 1: Using the build script (Linux/macOS)
```bash
chmod +x build.sh
./build.sh
```

### Option 2: Manual build
```bash
# Build the WASM package
wasm-pack build --target web --out-dir pkg

# Serve the application
python -m http.server 8000
# or use any other static file server
```

### Option 3: Using npm (if you have Node.js)
```bash
npm install
npm run dev
```

## Usage

1. Open your browser and navigate to `http://localhost:8000`
2. Click "📍 Get My Location" to allow location access
3. Click "🔄 Refresh Weather" to fetch current weather data
4. Watch the realistic weather simulation based on your local conditions!

## Project Structure

```
rusty-rain/
├── src/
│   ├── lib.rs          # Main WASM interface
│   ├── weather.rs      # Weather data structures
│   ├── renderer.rs     # Canvas rendering engine
│   ├── landscape.rs    # Terrain generation
│   ├── celestial.rs    # Sun/moon positioning
│   └── animation.rs    # Animation system
├── index.html          # Web interface
├── Cargo.toml          # Rust dependencies
├── package.json        # Node.js build scripts
└── README.md           # This file
```

## Technical Details

- **Rust + WASM**: Core simulation logic written in Rust, compiled to WebAssembly
- **Canvas 2D**: Rendering using HTML5 Canvas 2D API
- **Weather API**: Open-Meteo free weather API (no registration required)
- **Geolocation**: Browser geolocation API for location-based weather
- **Real-time Animation**: 60fps animation loop with delta-time updates

## Browser Compatibility

- Chrome/Chromium 57+
- Firefox 52+
- Safari 11+
- Edge 16+

## License

MIT License - feel free to use and modify as needed!

## Contributing

Contributions welcome! Some ideas for improvements:

- Add more weather effects (hail, aurora, etc.)
- Implement weather forecasting
- Add sound effects
- Create different landscape types
- Add seasonal changes
- Implement weather radar overlay

## Troubleshooting

**Build fails**: Make sure you have the latest Rust and wasm-pack installed
**Location not working**: Ensure you're serving over HTTPS or localhost
**Weather not loading**: Check browser console for CORS or network errors
**Animation stuttering**: Try a different browser or check system performance

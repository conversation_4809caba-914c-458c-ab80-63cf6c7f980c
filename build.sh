#!/bin/bash

echo "Building Rusty Rain Weather App..."

# Check if wasm-pack is installed
if ! command -v wasm-pack &> /dev/null; then
    echo "wasm-pack is not installed. Installing..."
    curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh
fi

# Build the WASM package
echo "Building WASM package..."
wasm-pack build --target web --out-dir pkg

if [ $? -eq 0 ]; then
    echo "Build successful!"
    echo "You can now serve the app with: python -m http.server 8000"
    echo "Then open http://localhost:8000 in your browser"
else
    echo "Build failed!"
    exit 1
fi

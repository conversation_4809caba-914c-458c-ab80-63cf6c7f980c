<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON> Demo - Weather Simulation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .canvas-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        #weather-canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .status {
            text-align: center;
            color: white;
            margin: 10px 0;
            opacity: 0.8;
        }
        
        .demo-note {
            background: rgba(255, 255, 0, 0.2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌦️ Rusty Rain Demo - Weather Simulation</h1>
        
        <div class="demo-note">
            <strong>Demo Version:</strong> This is a JavaScript demonstration of the Rust WASM weather app. 
            To build the full Rust version, install Rust and run the build script.
        </div>
        
        <div class="canvas-container">
            <canvas id="weather-canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <button onclick="setWeather('clear')">☀️ Clear</button>
            <button onclick="setWeather('cloudy')">⛅ Cloudy</button>
            <button onclick="setWeather('rain')">🌧️ Rain</button>
            <button onclick="setWeather('snow')">❄️ Snow</button>
            <button onclick="setWeather('storm')">⛈️ Storm</button>
            <button onclick="toggleTime()">🌙 Toggle Day/Night</button>
        </div>
        
        <div class="status" id="status">Demo weather simulation running</div>
    </div>

    <script>
        const canvas = document.getElementById('weather-canvas');
        const ctx = canvas.getContext('2d');
        
        let isDay = true;
        let currentWeather = 'clear';
        let animationTime = 0;
        let raindrops = [];
        let snowflakes = [];
        let clouds = [];
        let birds = [];
        let lightningTimer = 0;
        
        // Initialize particles
        function initParticles() {
            // Rain drops
            for (let i = 0; i < 100; i++) {
                raindrops.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    speed: 5 + Math.random() * 5
                });
            }
            
            // Snow flakes
            for (let i = 0; i < 50; i++) {
                snowflakes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    speed: 1 + Math.random() * 2,
                    size: 2 + Math.random() * 3
                });
            }
            
            // Clouds
            for (let i = 0; i < 5; i++) {
                clouds.push({
                    x: Math.random() * canvas.width,
                    y: 50 + Math.random() * 100,
                    size: 60 + Math.random() * 40,
                    speed: 0.5 + Math.random() * 0.5
                });
            }
            
            // Birds
            for (let i = 0; i < 3; i++) {
                birds.push({
                    x: Math.random() * canvas.width,
                    y: 100 + Math.random() * 100,
                    speed: 2 + Math.random() * 2,
                    wingPhase: Math.random() * Math.PI * 2
                });
            }
        }
        
        function drawSky() {
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height * 0.7);
            
            if (isDay) {
                gradient.addColorStop(0, '#87CEEB');
                gradient.addColorStop(1, '#B0E0E6');
            } else {
                gradient.addColorStop(0, '#191970');
                gradient.addColorStop(1, '#2F4F4F');
            }
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height * 0.7);
        }
        
        function drawLandscape() {
            // Ground
            ctx.fillStyle = '#2d5016';
            ctx.fillRect(0, canvas.height * 0.7, canvas.width, canvas.height * 0.3);
            
            // Hills
            ctx.fillStyle = '#1a3d0a';
            ctx.beginPath();
            ctx.moveTo(0, canvas.height * 0.7);
            
            for (let x = 0; x <= canvas.width; x += 20) {
                const height = Math.sin(x * 0.01 + animationTime * 0.001) * 50 + 
                              Math.sin(x * 0.005) * 30;
                ctx.lineTo(x, canvas.height * 0.7 - height);
            }
            
            ctx.lineTo(canvas.width, canvas.height * 0.7);
            ctx.closePath();
            ctx.fill();
        }
        
        function drawSun() {
            const x = canvas.width * 0.2 + Math.sin(animationTime * 0.001) * canvas.width * 0.6;
            const y = 100 + Math.abs(Math.sin(animationTime * 0.001)) * 100;
            
            // Sun glow
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, 40);
            gradient.addColorStop(0, 'rgba(255, 255, 0, 0.8)');
            gradient.addColorStop(1, 'rgba(255, 255, 0, 0)');
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, 40, 0, Math.PI * 2);
            ctx.fill();
            
            // Sun disc
            ctx.fillStyle = '#ffff00';
            ctx.beginPath();
            ctx.arc(x, y, 20, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawMoon() {
            const x = canvas.width * 0.8 - Math.sin(animationTime * 0.001) * canvas.width * 0.6;
            const y = 80 + Math.abs(Math.cos(animationTime * 0.001)) * 80;
            
            // Moon glow
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, 30);
            gradient.addColorStop(0, 'rgba(220, 220, 255, 0.6)');
            gradient.addColorStop(1, 'rgba(220, 220, 255, 0)');
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, Math.PI * 2);
            ctx.fill();
            
            // Moon disc
            ctx.fillStyle = '#f0f0f0';
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawClouds() {
            if (currentWeather === 'clear') return;
            
            ctx.fillStyle = 'rgba(200, 200, 200, 0.7)';
            
            clouds.forEach(cloud => {
                drawCloud(cloud.x, cloud.y, cloud.size);
                cloud.x += cloud.speed;
                if (cloud.x > canvas.width + cloud.size) {
                    cloud.x = -cloud.size;
                }
            });
        }
        
        function drawCloud(x, y, size) {
            ctx.beginPath();
            ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
            ctx.arc(x + size * 0.3, y, size * 0.4, 0, Math.PI * 2);
            ctx.arc(x - size * 0.3, y, size * 0.4, 0, Math.PI * 2);
            ctx.arc(x, y - size * 0.2, size * 0.3, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawRain() {
            if (currentWeather !== 'rain' && currentWeather !== 'storm') return;
            
            ctx.strokeStyle = 'rgba(100, 150, 255, 0.6)';
            ctx.lineWidth = 1;
            
            raindrops.forEach(drop => {
                ctx.beginPath();
                ctx.moveTo(drop.x, drop.y);
                ctx.lineTo(drop.x - 2, drop.y + 10);
                ctx.stroke();
                
                drop.y += drop.speed;
                drop.x -= 1;
                
                if (drop.y > canvas.height) {
                    drop.y = -10;
                    drop.x = Math.random() * canvas.width;
                }
            });
        }
        
        function drawSnow() {
            if (currentWeather !== 'snow') return;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            
            snowflakes.forEach(flake => {
                ctx.beginPath();
                ctx.arc(flake.x, flake.y, flake.size, 0, Math.PI * 2);
                ctx.fill();
                
                flake.y += flake.speed;
                flake.x += Math.sin(animationTime * 0.01 + flake.y * 0.01) * 0.5;
                
                if (flake.y > canvas.height) {
                    flake.y = -10;
                    flake.x = Math.random() * canvas.width;
                }
            });
        }
        
        function drawLightning() {
            if (currentWeather !== 'storm') return;
            
            lightningTimer++;
            if (lightningTimer > 120 && Math.random() < 0.02) {
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.lineWidth = 3;
                
                const x = Math.random() * canvas.width;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x + 20, canvas.height * 0.3);
                ctx.lineTo(x - 10, canvas.height * 0.5);
                ctx.lineTo(x + 15, canvas.height * 0.7);
                ctx.stroke();
                
                lightningTimer = 0;
            }
        }
        
        function drawBirds() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            
            birds.forEach(bird => {
                const wingOffset = Math.sin(bird.wingPhase) * 5;
                
                ctx.beginPath();
                ctx.moveTo(bird.x - 8, bird.y + wingOffset);
                ctx.lineTo(bird.x, bird.y);
                ctx.lineTo(bird.x + 8, bird.y + wingOffset);
                ctx.stroke();
                
                bird.x += bird.speed;
                bird.wingPhase += 0.3;
                
                if (bird.x > canvas.width + 20) {
                    bird.x = -20;
                    bird.y = 100 + Math.random() * 100;
                }
            });
        }
        
        function animate() {
            animationTime++;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw scene
            drawSky();
            drawLandscape();
            
            if (isDay) {
                drawSun();
            } else {
                drawMoon();
            }
            
            drawClouds();
            drawRain();
            drawSnow();
            drawLightning();
            drawBirds();
            
            requestAnimationFrame(animate);
        }
        
        window.setWeather = function(weather) {
            currentWeather = weather;
            document.getElementById('status').textContent = `Weather set to: ${weather}`;
        };
        
        window.toggleTime = function() {
            isDay = !isDay;
            document.getElementById('status').textContent = `Time set to: ${isDay ? 'Day' : 'Night'}`;
        };
        
        // Initialize and start
        initParticles();
        animate();
    </script>
</body>
</html>

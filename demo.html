<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON> Demo - Weather Simulation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .canvas-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        #weather-canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .status {
            text-align: center;
            color: white;
            margin: 10px 0;
            opacity: 0.8;
        }
        
        .demo-note {
            background: rgba(255, 255, 0, 0.2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌦️ Rusty Rain Demo - Weather Simulation</h1>
        
        <div class="demo-note">
            <strong>Demo Version:</strong> This is a JavaScript demonstration of the Rust WASM weather app. 
            To build the full Rust version, install Rust and run the build script.
        </div>
        
        <div class="canvas-container">
            <canvas id="weather-canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <button onclick="setWeather('clear')">☀️ Clear</button>
            <button onclick="setWeather('cloudy')">⛅ Cloudy</button>
            <button onclick="setWeather('rain')">🌧️ Rain</button>
            <button onclick="setWeather('snow')">❄️ Snow</button>
            <button onclick="setWeather('storm')">⛈️ Storm</button>
            <button onclick="toggleTime()">🌙 Toggle Day/Night</button>
        </div>
        
        <div class="status" id="status">Demo weather simulation running</div>
    </div>

    <script>
        const canvas = document.getElementById('weather-canvas');
        const ctx = canvas.getContext('2d');
        
        let isDay = true;
        let currentWeather = 'clear';
        let animationTime = 0;
        let raindrops = [];
        let snowflakes = [];
        let clouds = [];
        let birds = [];
        let characters = [];
        let chatBubbles = [];
        let lightningTimer = 0;
        let chatTimer = 0;
        
        // Initialize particles
        function initParticles() {
            // Rain drops
            for (let i = 0; i < 100; i++) {
                raindrops.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    speed: 5 + Math.random() * 5
                });
            }
            
            // Snow flakes
            for (let i = 0; i < 50; i++) {
                snowflakes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    speed: 1 + Math.random() * 2,
                    size: 2 + Math.random() * 3
                });
            }
            
            // Clouds
            for (let i = 0; i < 5; i++) {
                clouds.push({
                    x: Math.random() * canvas.width,
                    y: 50 + Math.random() * 100,
                    size: 60 + Math.random() * 40,
                    speed: 0.5 + Math.random() * 0.5
                });
            }
            
            // Birds
            for (let i = 0; i < 3; i++) {
                birds.push({
                    x: Math.random() * canvas.width,
                    y: 100 + Math.random() * 100,
                    speed: 2 + Math.random() * 2,
                    wingPhase: Math.random() * Math.PI * 2
                });
            }

            // Characters - more active and numerous
            const characterTypes = ['person', 'child', 'elder', 'dog'];
            for (let i = 0; i < 8; i++) { // More characters
                characters.push({
                    x: Math.random() * canvas.width,
                    y: canvas.height * 0.7 + 50, // Ground level
                    speed: 25 + Math.random() * 25, // Much faster walking
                    direction: Math.random() > 0.5 ? 1 : -1,
                    walkPhase: Math.random() * Math.PI * 2,
                    type: characterTypes[Math.floor(Math.random() * characterTypes.length)],
                    isChatting: false,
                    chatPartnerId: null,
                    chatDuration: 0,
                    idleTimer: 0
                });
            }
        }
        
        function drawSky() {
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height * 0.7);
            
            if (isDay) {
                gradient.addColorStop(0, '#87CEEB');
                gradient.addColorStop(1, '#B0E0E6');
            } else {
                gradient.addColorStop(0, '#191970');
                gradient.addColorStop(1, '#2F4F4F');
            }
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height * 0.7);
        }
        
        function drawLandscape() {
            // Ground
            ctx.fillStyle = '#2d5016';
            ctx.fillRect(0, canvas.height * 0.7, canvas.width, canvas.height * 0.3);
            
            // Hills
            ctx.fillStyle = '#1a3d0a';
            ctx.beginPath();
            ctx.moveTo(0, canvas.height * 0.7);
            
            for (let x = 0; x <= canvas.width; x += 20) {
                const height = Math.sin(x * 0.01 + animationTime * 0.001) * 50 + 
                              Math.sin(x * 0.005) * 30;
                ctx.lineTo(x, canvas.height * 0.7 - height);
            }
            
            ctx.lineTo(canvas.width, canvas.height * 0.7);
            ctx.closePath();
            ctx.fill();
        }
        
        function drawSun() {
            const x = canvas.width * 0.2 + Math.sin(animationTime * 0.001) * canvas.width * 0.6;
            const y = 100 + Math.abs(Math.sin(animationTime * 0.001)) * 100;
            
            // Sun glow
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, 40);
            gradient.addColorStop(0, 'rgba(255, 255, 0, 0.8)');
            gradient.addColorStop(1, 'rgba(255, 255, 0, 0)');
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, 40, 0, Math.PI * 2);
            ctx.fill();
            
            // Sun disc
            ctx.fillStyle = '#ffff00';
            ctx.beginPath();
            ctx.arc(x, y, 20, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawMoon() {
            const x = canvas.width * 0.8 - Math.sin(animationTime * 0.001) * canvas.width * 0.6;
            const y = 80 + Math.abs(Math.cos(animationTime * 0.001)) * 80;
            
            // Moon glow
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, 30);
            gradient.addColorStop(0, 'rgba(220, 220, 255, 0.6)');
            gradient.addColorStop(1, 'rgba(220, 220, 255, 0)');
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, Math.PI * 2);
            ctx.fill();
            
            // Moon disc
            ctx.fillStyle = '#f0f0f0';
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawClouds() {
            if (currentWeather === 'clear') return;
            
            ctx.fillStyle = 'rgba(200, 200, 200, 0.7)';
            
            clouds.forEach(cloud => {
                drawCloud(cloud.x, cloud.y, cloud.size);
                cloud.x += cloud.speed;
                if (cloud.x > canvas.width + cloud.size) {
                    cloud.x = -cloud.size;
                }
            });
        }
        
        function drawCloud(x, y, size) {
            ctx.beginPath();
            ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
            ctx.arc(x + size * 0.3, y, size * 0.4, 0, Math.PI * 2);
            ctx.arc(x - size * 0.3, y, size * 0.4, 0, Math.PI * 2);
            ctx.arc(x, y - size * 0.2, size * 0.3, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawRain() {
            if (currentWeather !== 'rain' && currentWeather !== 'storm') return;
            
            ctx.strokeStyle = 'rgba(100, 150, 255, 0.6)';
            ctx.lineWidth = 1;
            
            raindrops.forEach(drop => {
                ctx.beginPath();
                ctx.moveTo(drop.x, drop.y);
                ctx.lineTo(drop.x - 2, drop.y + 10);
                ctx.stroke();
                
                drop.y += drop.speed;
                drop.x -= 1;
                
                if (drop.y > canvas.height) {
                    drop.y = -10;
                    drop.x = Math.random() * canvas.width;
                }
            });
        }
        
        function drawSnow() {
            if (currentWeather !== 'snow') return;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            
            snowflakes.forEach(flake => {
                ctx.beginPath();
                ctx.arc(flake.x, flake.y, flake.size, 0, Math.PI * 2);
                ctx.fill();
                
                flake.y += flake.speed;
                flake.x += Math.sin(animationTime * 0.01 + flake.y * 0.01) * 0.5;
                
                if (flake.y > canvas.height) {
                    flake.y = -10;
                    flake.x = Math.random() * canvas.width;
                }
            });
        }
        
        function drawLightning() {
            if (currentWeather !== 'storm') return;
            
            lightningTimer++;
            if (lightningTimer > 120 && Math.random() < 0.02) {
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.lineWidth = 3;
                
                const x = Math.random() * canvas.width;
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x + 20, canvas.height * 0.3);
                ctx.lineTo(x - 10, canvas.height * 0.5);
                ctx.lineTo(x + 15, canvas.height * 0.7);
                ctx.stroke();
                
                lightningTimer = 0;
            }
        }
        
        function drawBirds() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;

            birds.forEach(bird => {
                const wingOffset = Math.sin(bird.wingPhase) * 5;

                ctx.beginPath();
                ctx.moveTo(bird.x - 8, bird.y + wingOffset);
                ctx.lineTo(bird.x, bird.y);
                ctx.lineTo(bird.x + 8, bird.y + wingOffset);
                ctx.stroke();

                bird.x += bird.speed;
                bird.wingPhase += 0.3;

                if (bird.x > canvas.width + 20) {
                    bird.x = -20;
                    bird.y = 100 + Math.random() * 100;
                }
            });
        }

        function updateCharacters() {
            characters.forEach((character, i) => {
                if (character.isChatting) {
                    character.chatDuration += 1;
                    if (character.chatDuration > 90) { // Much shorter chats - only 1.5 seconds at 60fps
                        character.isChatting = false;
                        character.chatPartnerId = null;
                        character.chatDuration = 0;
                        character.idleTimer = 15 + Math.random() * 15; // Very brief pause (0.25-0.5 seconds)
                    }
                } else if (character.idleTimer > 0) {
                    character.idleTimer--;
                } else {
                    // Much more active walking
                    character.x += character.direction * character.speed * 0.016; // ~60fps
                    character.walkPhase += 0.15; // Faster walking animation

                    // Wrap around screen
                    if (character.x > canvas.width + 50) {
                        character.x = -50;
                        character.direction = 1;
                    } else if (character.x < -50) {
                        character.x = canvas.width + 50;
                        character.direction = -1;
                    }

                    // Much more frequent direction changes for dynamic movement
                    if (Math.random() < 0.008) { // 4x more likely to change direction
                        character.direction *= -1;
                    }

                    // Occasionally change speed for variety
                    if (Math.random() < 0.001) {
                        character.speed = 25 + Math.random() * 25;
                    }
                }
            });

            // Check for meetings
            checkCharacterMeetings();

            // Much less frequent conversations - people should walk more than chat
            if (chatTimer > 480 && Math.random() < 0.03) { // Every 8+ seconds, much lower chance
                tryStartConversation();
                chatTimer = 0;
            }
        }

        function checkCharacterMeetings() {
            for (let i = 0; i < characters.length; i++) {
                for (let j = i + 1; j < characters.length; j++) {
                    const char1 = characters[i];
                    const char2 = characters[j];
                    const distance = Math.abs(char1.x - char2.x);

                    if (distance < 60 && !char1.isChatting && !char2.isChatting) {
                        startConversation(i, j);
                    }
                }
            }
        }

        function tryStartConversation() {
            const available = characters
                .map((char, index) => ({ char, index }))
                .filter(item => !item.char.isChatting);

            if (available.length >= 2) {
                const idx1 = available[Math.floor(Math.random() * available.length)].index;
                const idx2 = available[Math.floor(Math.random() * available.length)].index;

                if (idx1 !== idx2) {
                    // Move characters closer
                    const char1 = characters[idx1];
                    const char2 = characters[idx2];

                    if (Math.abs(char1.x - char2.x) > 100) {
                        const meetingPoint = (char1.x + char2.x) / 2;
                        char1.x = meetingPoint - 30;
                        char2.x = meetingPoint + 30;
                    }

                    startConversation(idx1, idx2);
                }
            }
        }

        function getWeatherMessages(weather) {
            const temp = 20; // Default temperature for demo

            switch(weather) {
                case 'clear':
                    if (isDay) {
                        if (temp > 25) {
                            return [
                                "Beautiful sunny day!", "Perfect weather for a picnic!",
                                "Love this sunshine!", "Great day to be outside!",
                                "Couldn't ask for better weather!", "This heat feels amazing!",
                                "Perfect beach weather!"
                            ];
                        } else {
                            return [
                                "Nice weather today!", "Perfect day for a walk!",
                                "Love the clear skies!", "Great day to be outside!",
                                "Beautiful day, isn't it?", "Perfect temperature!"
                            ];
                        }
                    } else {
                        return [
                            "Beautiful clear night!", "Look at those stars!",
                            "Perfect evening for a walk!", "Love these clear nights!",
                            "Great night to be out!"
                        ];
                    }
                case 'cloudy':
                    return [
                        "Nice mix of sun and clouds!", "Perfect weather, not too hot!",
                        "Love these partly cloudy days!", "Great weather for walking!",
                        "Just the right amount of clouds!", "Comfortable weather today!"
                    ];
                case 'rain':
                    return [
                        "Getting soaked out here!", "Forgot my umbrella again!",
                        "This rain is heavy!", "Hope it stops soon!",
                        "At least the plants are happy!", "Good day to stay inside!",
                        "This weather is depressing...", "Wish I had an umbrella!",
                        "Rain, rain, go away!"
                    ];
                case 'snow':
                    return [
                        "It's snowing!", "Winter wonderland out here!",
                        "Hope the roads aren't icy!", "Beautiful snowfall!",
                        "Time to build a snowman!", "Slippery out here!",
                        "Love the snow!", "Winter is here!", "So cold and snowy!"
                    ];
                case 'storm':
                    return [
                        "This storm is scary!", "That lightning was close!",
                        "We should get inside!", "This thunder is loud!",
                        "Dangerous weather today!", "Hope the power doesn't go out!",
                        "This storm came out of nowhere!", "Better find shelter!",
                        "Wild weather today!"
                    ];
                default:
                    return [
                        "Nice day, isn't it?", "How's it going?",
                        "Lovely weather!", "Great day for a walk!"
                    ];
            }
        }

        function startConversation(idx1, idx2) {
            const char1 = characters[idx1];
            const char2 = characters[idx2];

            char1.isChatting = true;
            char1.chatPartnerId = idx2;
            char1.chatDuration = 0;

            char2.isChatting = true;
            char2.chatPartnerId = idx1;
            char2.chatDuration = 0;

            // Get weather-appropriate messages
            const messages = getWeatherMessages(currentWeather);

            const message1 = messages[Math.floor(Math.random() * messages.length)];
            const message2 = messages[Math.floor(Math.random() * messages.length)];

            chatBubbles.push({
                x: char1.x, y: char1.y - 40, message: message1,
                duration: 0, maxDuration: 180, characterId: idx1
            });

            setTimeout(() => {
                chatBubbles.push({
                    x: char2.x, y: char2.y - 40, message: message2,
                    duration: 0, maxDuration: 180, characterId: idx2
                });
            }, 1000);
        }

        function drawCharacters() {
            characters.forEach((character, i) => {
                const x = character.x;
                const y = character.y;

                // Character colors and sizes
                const styles = {
                    person: { color: '#4a90e2', size: 20 },
                    child: { color: '#ff6b6b', size: 14 },
                    elder: { color: '#8e44ad', size: 20 },
                    dog: { color: '#8b4513', size: 10 }
                };

                const style = styles[character.type];

                if (character.type === 'dog') {
                    drawDog(x, y, character.walkPhase, character.direction);
                } else {
                    drawPerson(x, y, character.walkPhase, character.direction, style.color, style.size);
                }

                // Chat indicator
                if (character.isChatting) {
                    ctx.fillStyle = 'rgba(255, 255, 0, 0.3)';
                    ctx.beginPath();
                    ctx.arc(x, y - style.size, style.size * 0.8, 0, Math.PI * 2);
                    ctx.fill();
                }
            });
        }

        function drawPerson(x, y, walkPhase, direction, color, size) {
            // Head
            ctx.fillStyle = '#ffdbac';
            ctx.beginPath();
            ctx.arc(x, y - size * 1.5, size * 0.3, 0, Math.PI * 2);
            ctx.fill();

            // Body
            ctx.fillStyle = color;
            ctx.fillRect(x - size * 0.2, y - size * 1.2, size * 0.4, size * 0.8);

            // Legs
            const legOffset = Math.sin(walkPhase) * 5;
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;

            ctx.beginPath();
            ctx.moveTo(x - size * 0.1, y - size * 0.4);
            ctx.lineTo(x - size * 0.1 + legOffset * direction, y);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(x + size * 0.1, y - size * 0.4);
            ctx.lineTo(x + size * 0.1 - legOffset * direction, y);
            ctx.stroke();

            // Arms
            const armOffset = Math.sin(walkPhase) * 3;
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.moveTo(x - size * 0.2, y - size * 1.0);
            ctx.lineTo(x - size * 0.3 - armOffset * direction, y - size * 0.7);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(x + size * 0.2, y - size * 1.0);
            ctx.lineTo(x + size * 0.3 + armOffset * direction, y - size * 0.7);
            ctx.stroke();
        }

        function drawDog(x, y, walkPhase, direction) {
            const size = 10;

            // Body
            ctx.fillStyle = '#8b4513';
            ctx.fillRect(x - size * 0.8, y - size * 0.6, size * 1.6, size * 0.4);

            // Head
            ctx.beginPath();
            ctx.arc(x + direction * size * 0.8, y - size * 0.4, size * 0.3, 0, Math.PI * 2);
            ctx.fill();

            // Legs
            const legOffset = Math.sin(walkPhase * 2) * 2;
            ctx.strokeStyle = '#654321';
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.moveTo(x + size * 0.4, y - size * 0.2);
            ctx.lineTo(x + size * 0.4 + legOffset, y);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(x - size * 0.4, y - size * 0.2);
            ctx.lineTo(x - size * 0.4 - legOffset, y);
            ctx.stroke();

            // Tail
            const tailWag = Math.sin(walkPhase * 3) * 10;
            ctx.beginPath();
            ctx.moveTo(x - direction * size * 0.8, y - size * 0.4);
            ctx.lineTo(x - direction * size * 1.2, y - size * 0.6 + tailWag);
            ctx.stroke();
        }

        function updateChatBubbles() {
            chatBubbles.forEach(bubble => {
                bubble.duration++;

                // Update position to follow character
                if (bubble.characterId < characters.length) {
                    bubble.x = characters[bubble.characterId].x;
                    bubble.y = characters[bubble.characterId].y - 40;
                }
            });

            // Remove expired bubbles
            chatBubbles = chatBubbles.filter(bubble => bubble.duration < bubble.maxDuration);
        }

        function drawChatBubbles() {
            chatBubbles.forEach(bubble => {
                const x = bubble.x;
                const y = bubble.y;
                const text = bubble.message;

                // Measure text
                ctx.font = '12px Arial';
                const textWidth = ctx.measureText(text).width;
                const bubbleWidth = textWidth + 20;
                const bubbleHeight = 25;

                // Fade effect
                const alpha = Math.max(0, Math.min(1, (bubble.maxDuration - bubble.duration) / bubble.maxDuration));

                // Weather-appropriate bubble colors
                let bgColor, borderColor, textColor;

                if (text.includes('storm') || text.includes('scary') || text.includes('dangerous')) {
                    bgColor = `rgba(255, 200, 200, ${alpha * 0.9})`;
                    borderColor = `rgba(200, 0, 0, ${alpha * 0.8})`;
                    textColor = `rgba(100, 0, 0, ${alpha})`;
                } else if (text.includes('snow') || text.includes('cold')) {
                    bgColor = `rgba(200, 220, 255, ${alpha * 0.9})`;
                    borderColor = `rgba(100, 150, 255, ${alpha * 0.8})`;
                    textColor = `rgba(0, 50, 150, ${alpha})`;
                } else if (text.includes('rain') || text.includes('wet') || text.includes('umbrella')) {
                    bgColor = `rgba(200, 200, 255, ${alpha * 0.9})`;
                    borderColor = `rgba(100, 100, 200, ${alpha * 0.8})`;
                    textColor = `rgba(50, 50, 150, ${alpha})`;
                } else if (text.includes('sunny') || text.includes('sunshine') || text.includes('beautiful')) {
                    bgColor = `rgba(255, 255, 200, ${alpha * 0.9})`;
                    borderColor = `rgba(255, 200, 0, ${alpha * 0.8})`;
                    textColor = `rgba(150, 100, 0, ${alpha})`;
                } else {
                    bgColor = `rgba(255, 255, 255, ${alpha * 0.9})`;
                    borderColor = `rgba(0, 0, 0, ${alpha * 0.8})`;
                    textColor = `rgba(0, 0, 0, ${alpha})`;
                }

                // Bubble background
                ctx.fillStyle = bgColor;
                ctx.strokeStyle = borderColor;
                ctx.lineWidth = 1;

                // Bubble shape
                ctx.beginPath();
                ctx.roundRect(x - bubbleWidth / 2, y - bubbleHeight, bubbleWidth, bubbleHeight, 5);
                ctx.fill();
                ctx.stroke();

                // Bubble pointer
                ctx.beginPath();
                ctx.moveTo(x - 5, y - 5);
                ctx.lineTo(x, y);
                ctx.lineTo(x + 5, y - 5);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();

                // Text
                ctx.fillStyle = textColor;
                ctx.textAlign = 'center';
                ctx.fillText(text, x, y - bubbleHeight / 2 + 4);
            });
        }
        
        function animate() {
            animationTime++;
            chatTimer++;

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw scene
            drawSky();
            drawLandscape();

            if (isDay) {
                drawSun();
            } else {
                drawMoon();
            }

            drawClouds();
            drawRain();
            drawSnow();
            drawLightning();
            drawBirds();
            updateCharacters();
            drawCharacters();
            updateChatBubbles();
            drawChatBubbles();

            requestAnimationFrame(animate);
        }
        
        window.setWeather = function(weather) {
            currentWeather = weather;
            document.getElementById('status').textContent = `Weather set to: ${weather}`;
        };
        
        window.toggleTime = function() {
            isDay = !isDay;
            document.getElementById('status').textContent = `Time set to: ${isDay ? 'Day' : 'Night'}`;
        };
        
        // Initialize and start
        initParticles();
        animate();
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rusty Rain - Weather Simulation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .canvas-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        #weather-canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .weather-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .weather-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            color: white;
            backdrop-filter: blur(5px);
        }
        
        .weather-card h3 {
            margin: 0 0 10px 0;
            color: #fff;
        }
        
        .weather-card p {
            margin: 5px 0;
            opacity: 0.9;
        }
        
        .status {
            text-align: center;
            color: white;
            margin: 10px 0;
            opacity: 0.8;
        }
        
        .loading {
            text-align: center;
            color: white;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            #weather-canvas {
                width: 100%;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌦️ Rusty Rain Weather Simulation</h1>
        
        <div class="canvas-container">
            <canvas id="weather-canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <button onclick="getLocation()">📍 Get My Location</button>
            <button onclick="fetchWeather()">🔄 Refresh Weather</button>
            <button onclick="toggleAnimation()">⏯️ Toggle Animation</button>
        </div>
        
        <div class="status" id="status">Click "Get My Location" to start</div>
        
        <div class="weather-info" id="weather-info" style="display: none;">
            <div class="weather-card">
                <h3>Current Weather</h3>
                <p id="weather-desc">-</p>
                <p id="weather-temp">-</p>
                <p id="weather-location">-</p>
            </div>
            <div class="weather-card">
                <h3>Conditions</h3>
                <p id="weather-humidity">Humidity: -</p>
                <p id="weather-pressure">Pressure: -</p>
                <p id="weather-visibility">Visibility: -</p>
            </div>
            <div class="weather-card">
                <h3>Wind & Clouds</h3>
                <p id="weather-wind">Wind: -</p>
                <p id="weather-clouds">Clouds: -</p>
                <p id="weather-precipitation">Precipitation: -</p>
            </div>
        </div>
    </div>

    <script type="module">
        import init, { WeatherApp } from './pkg/rusty_rain.js';
        
        let app = null;
        let animationId = null;
        let isAnimating = true;
        let currentLat = null;
        let currentLon = null;
        
        async function run() {
            try {
                await init();
                
                const canvas = document.getElementById('weather-canvas');
                app = new WeatherApp(canvas);
                
                // Start animation loop
                startAnimation();
                
                document.getElementById('status').textContent = 'Weather simulation ready! Get your location to see real weather.';
            } catch (error) {
                console.error('Failed to initialize WASM:', error);
                document.getElementById('status').textContent = 'Failed to load weather simulation. Please refresh the page.';
            }
        }
        
        function startAnimation() {
            if (!isAnimating || !app) return;
            
            try {
                app.update();
            } catch (error) {
                console.error('Animation error:', error);
            }
            
            animationId = requestAnimationFrame(startAnimation);
        }
        
        function stopAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
        }
        
        window.toggleAnimation = function() {
            isAnimating = !isAnimating;
            if (isAnimating) {
                startAnimation();
            } else {
                stopAnimation();
            }
        };
        
        window.getLocation = function() {
            if (!navigator.geolocation) {
                document.getElementById('status').textContent = 'Geolocation is not supported by this browser.';
                return;
            }
            
            document.getElementById('status').textContent = 'Getting your location...';
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    currentLat = position.coords.latitude;
                    currentLon = position.coords.longitude;
                    document.getElementById('status').textContent = `Location found: ${currentLat.toFixed(2)}, ${currentLon.toFixed(2)}`;
                    fetchWeather();
                },
                function(error) {
                    document.getElementById('status').textContent = 'Unable to get location. Using demo weather data.';
                    loadDemoWeather();
                }
            );
        };
        
        window.fetchWeather = async function() {
            if (!currentLat || !currentLon) {
                document.getElementById('status').textContent = 'Please get your location first.';
                return;
            }
            
            document.getElementById('status').textContent = 'Fetching weather data...';
            
            try {
                // Using Open-Meteo API (free, no API key required)
                const response = await fetch(
                    `https://api.open-meteo.com/v1/forecast?latitude=${currentLat}&longitude=${currentLon}&current=temperature_2m,relative_humidity_2m,surface_pressure,cloud_cover,precipitation,weather_code,wind_speed_10m,wind_direction_10m,visibility&timezone=auto`
                );
                
                if (!response.ok) {
                    throw new Error('Weather API request failed');
                }
                
                const data = await response.json();
                const current = data.current;
                
                // Convert to our weather data format
                const weatherData = {
                    location: `${currentLat.toFixed(2)}, ${currentLon.toFixed(2)}`,
                    temperature: current.temperature_2m,
                    humidity: current.relative_humidity_2m,
                    pressure: current.surface_pressure,
                    wind_speed: current.wind_speed_10m,
                    wind_direction: current.wind_direction_10m,
                    visibility: current.visibility || 10000,
                    cloud_cover: current.cloud_cover,
                    precipitation: current.precipitation,
                    weather_code: current.weather_code,
                    description: getWeatherDescription(current.weather_code),
                    is_day: isDay(),
                    sunrise: null,
                    sunset: null
                };
                
                // Send weather data to WASM
                if (app) {
                    app.set_weather_data(JSON.stringify(weatherData));
                }
                
                // Update UI
                updateWeatherDisplay(weatherData);
                document.getElementById('status').textContent = 'Weather data updated successfully!';
                
            } catch (error) {
                console.error('Weather fetch error:', error);
                document.getElementById('status').textContent = 'Failed to fetch weather data. Using demo data.';
                loadDemoWeather();
            }
        };
        
        function loadDemoWeather() {
            const demoWeather = {
                location: "Demo Location",
                temperature: 22,
                humidity: 65,
                pressure: 1013,
                wind_speed: 15,
                wind_direction: 180,
                visibility: 10000,
                cloud_cover: 40,
                precipitation: 0,
                weather_code: 2,
                description: "Partly Cloudy",
                is_day: isDay(),
                sunrise: null,
                sunset: null
            };
            
            if (app) {
                app.set_weather_data(JSON.stringify(demoWeather));
            }
            
            updateWeatherDisplay(demoWeather);
        }
        
        function updateWeatherDisplay(weather) {
            document.getElementById('weather-desc').textContent = weather.description;
            document.getElementById('weather-temp').textContent = `${weather.temperature}°C`;
            document.getElementById('weather-location').textContent = weather.location;
            document.getElementById('weather-humidity').textContent = `Humidity: ${weather.humidity}%`;
            document.getElementById('weather-pressure').textContent = `Pressure: ${weather.pressure} hPa`;
            document.getElementById('weather-visibility').textContent = `Visibility: ${(weather.visibility / 1000).toFixed(1)} km`;
            document.getElementById('weather-wind').textContent = `Wind: ${weather.wind_speed} km/h`;
            document.getElementById('weather-clouds').textContent = `Clouds: ${weather.cloud_cover}%`;
            document.getElementById('weather-precipitation').textContent = `Precipitation: ${weather.precipitation} mm`;
            
            document.getElementById('weather-info').style.display = 'grid';
        }
        
        function getWeatherDescription(code) {
            const descriptions = {
                0: "Clear Sky",
                1: "Mainly Clear",
                2: "Partly Cloudy",
                3: "Overcast",
                45: "Fog",
                48: "Depositing Rime Fog",
                51: "Light Drizzle",
                53: "Moderate Drizzle",
                55: "Dense Drizzle",
                61: "Slight Rain",
                63: "Moderate Rain",
                65: "Heavy Rain",
                71: "Slight Snow",
                73: "Moderate Snow",
                75: "Heavy Snow",
                80: "Slight Rain Showers",
                81: "Moderate Rain Showers",
                82: "Violent Rain Showers",
                85: "Slight Snow Showers",
                86: "Heavy Snow Showers",
                95: "Thunderstorm",
                96: "Thunderstorm with Hail",
                99: "Thunderstorm with Heavy Hail"
            };
            return descriptions[code] || "Unknown";
        }
        
        function isDay() {
            const hour = new Date().getHours();
            return hour >= 6 && hour < 18;
        }
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (app) {
                const canvas = document.getElementById('weather-canvas');
                app.resize(canvas.width, canvas.height);
            }
        });
        
        // Initialize the app
        run();
    </script>
</body>
</html>

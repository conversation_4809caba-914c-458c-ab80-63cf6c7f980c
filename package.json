{"name": "rusty-rain", "version": "1.0.0", "description": "A WASM weather simulation app built with Rust", "main": "index.html", "scripts": {"build": "wasm-pack build --target web --out-dir pkg", "serve": "python -m http.server 8000", "dev": "npm run build && npm run serve"}, "keywords": ["rust", "wasm", "weather", "simulation"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"wasm-pack": "^0.12.1"}}
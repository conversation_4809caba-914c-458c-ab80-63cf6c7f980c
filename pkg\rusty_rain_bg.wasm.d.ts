/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export const __wbg_weatherapp_free: (a: number, b: number) => void;
export const weatherapp_new: (a: any) => [number, number, number];
export const weatherapp_update: (a: number) => [number, number];
export const weatherapp_set_weather_data: (a: number, b: number, c: number) => [number, number];
export const weatherapp_resize: (a: number, b: number, c: number) => void;
export const main: () => void;
export const __wbindgen_exn_store: (a: number) => void;
export const __externref_table_alloc: () => number;
export const __wbindgen_export_2: WebAssembly.Table;
export const __wbindgen_malloc: (a: number, b: number) => number;
export const __wbindgen_realloc: (a: number, b: number, c: number, d: number) => number;
export const __externref_table_dealloc: (a: number) => void;
export const __wbindgen_start: () => void;

use rand::Rng;

pub struct AnimationSystem {
    pub cloud_offset: f64,
    pub rain_offset: f64,
    pub snow_offset: f64,
    pub birds: Vec<Bird>,
    pub characters: Vec<Character>,
    pub chat_bubbles: Vec<ChatBubble>,
    pub cars: Vec<Car>,
    lightning_timer: f64,
    lightning_frequency: f64,
    chat_timer: f64,
    car_spawn_timer: f64,
}

pub struct Bird {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub wing_phase: f64,
    pub wing_speed: f64,
}

pub struct Car {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub direction: f64, // -1 for left, 1 for right
    pub car_type: CarType,
    pub color: CarColor,
}

pub struct Character {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub direction: f64, // -1 for left, 1 for right
    pub walk_phase: f64,
    pub character_type: CharacterType,
    pub is_chatting: bool,
    pub chat_partner_id: Option<usize>,
    pub chat_duration: f64,
    pub idle_timer: f64,
    pub is_fighting: bool,
    pub fight_partner_id: Option<usize>,
    pub fight_duration: f64,
    pub fight_intensity: f64,
    pub mood: CharacterMood,
    pub anger_level: f64,
}

pub struct ChatBubble {
    pub x: f64,
    pub y: f64,
    pub message: String,
    pub duration: f64,
    pub max_duration: f64,
    pub character_id: usize,
    pub bubble_type: BubbleType,
}

#[derive(Clone, Copy)]
pub enum CharacterType {
    Person,
    Dog,
    Child,
    Elder,
}

#[derive(Clone, Copy)]
pub enum CharacterMood {
    Happy,
    Neutral,
    Annoyed,
    Angry,
    Fighting,
}

#[derive(Clone, Copy)]
pub enum BubbleType {
    Normal,
    Angry,
    Fighting,
}

#[derive(Clone, Copy)]
pub enum CarType {
    Sedan,
    SUV,
    Truck,
    Compact,
}

#[derive(Clone, Copy)]
pub enum CarColor {
    Red,
    Blue,
    White,
    Black,
    Silver,
    Green,
}

impl AnimationSystem {
    pub fn new() -> Self {
        let mut rng = rand::thread_rng();
        let mut birds = Vec::new();
        let mut characters = Vec::new();

        // Create some birds
        for _ in 0..5 {
            birds.push(Bird {
                x: rng.gen_range(0.0..800.0),
                y: rng.gen_range(50.0..200.0),
                speed: rng.gen_range(20.0..50.0),
                wing_phase: rng.gen_range(0.0..1.0),
                wing_speed: rng.gen_range(3.0..6.0),
            });
        }

        // Create some characters
        for _ in 0..6 {
            let character_types = [CharacterType::Person, CharacterType::Dog, CharacterType::Child, CharacterType::Elder];
            characters.push(Character {
                x: rng.gen_range(0.0..800.0),
                y: 0.0, // Will be set based on ground level
                speed: rng.gen_range(15.0..35.0),
                direction: if rng.gen_bool(0.5) { 1.0 } else { -1.0 },
                walk_phase: rng.gen_range(0.0..1.0),
                character_type: character_types[rng.gen_range(0..character_types.len())],
                is_chatting: false,
                chat_partner_id: None,
                chat_duration: 0.0,
                idle_timer: 0.0,
                is_fighting: false,
                fight_partner_id: None,
                fight_duration: 0.0,
                fight_intensity: 0.0,
                mood: CharacterMood::Neutral,
                anger_level: rng.gen_range(0.0..0.3), // Some characters are more prone to anger
            });
        }

        Self {
            cloud_offset: 0.0,
            rain_offset: 0.0,
            snow_offset: 0.0,
            birds,
            characters,
            chat_bubbles: Vec::new(),
            cars: Vec::new(),
            lightning_timer: 0.0,
            lightning_frequency: 3.0, // Lightning every 3 seconds on average
            chat_timer: 0.0,
            car_spawn_timer: 0.0,
        }
    }

    pub fn update(&mut self, delta_time: f64) {
        self.update_with_weather(delta_time, None);
    }

    pub fn update_with_weather(&mut self, delta_time: f64, weather_data: Option<&crate::WeatherData>) {
        // Update cloud movement
        self.cloud_offset += delta_time * 10.0; // Slow cloud movement

        // Update precipitation
        self.rain_offset += delta_time * 200.0; // Fast rain drops
        self.snow_offset += delta_time * 50.0;  // Slower snow

        // Update birds
        for bird in &mut self.birds {
            bird.x += bird.speed * delta_time;
            bird.wing_phase += bird.wing_speed * delta_time;

            // Wrap birds around screen
            if bird.x > 900.0 {
                bird.x = -100.0;
                // Randomize height when bird wraps
                let mut rng = rand::thread_rng();
                bird.y = rng.gen_range(50.0..200.0);
            }
        }

        // Update characters with weather context
        self.update_characters_with_weather(delta_time, weather_data);

        // Update cars
        self.update_cars(delta_time);

        // Update chat bubbles
        self.update_chat_bubbles(delta_time);

        // Update lightning timer
        self.lightning_timer += delta_time;

        // Update chat timer
        self.chat_timer += delta_time;

        // Update car spawn timer
        self.car_spawn_timer += delta_time;
    }

    pub fn should_show_lightning(&mut self) -> bool {
        let mut rng = rand::thread_rng();
        if self.lightning_timer > self.lightning_frequency {
            self.lightning_timer = 0.0;
            self.lightning_frequency = rng.gen_range(2.0..5.0); // Random interval
            rng.gen_bool(0.3) // 30% chance to show lightning
        } else {
            false
        }
    }

    fn update_characters_with_weather(&mut self, delta_time: f64, weather_data: Option<&crate::WeatherData>) {
        let ground_y = 420.0; // Ground level
        let mut rng = rand::thread_rng();

        // Update each character
        for i in 0..self.characters.len() {
            let character = &mut self.characters[i];
            character.y = ground_y;

            if character.is_chatting {
                // Handle chatting behavior
                character.chat_duration += delta_time;
                if character.chat_duration > 3.0 { // Chat for only 3 seconds (much shorter)
                    character.is_chatting = false;
                    character.chat_partner_id = None;
                    character.chat_duration = 0.0;
                    character.idle_timer = rng.gen_range(1.0..2.0); // Shorter idle time too
                }
            } else if character.idle_timer > 0.0 {
                // Idle period after chatting
                character.idle_timer -= delta_time;
            } else {
                // Normal walking behavior
                character.x += character.direction * character.speed * delta_time;
                character.walk_phase += delta_time * 4.0;

                // Wrap around screen
                if character.x > 850.0 {
                    character.x = -50.0;
                    character.direction = 1.0;
                } else if character.x < -50.0 {
                    character.x = 850.0;
                    character.direction = -1.0;
                }

                // Randomly change direction
                if rng.gen_range(0.0..1.0) < 0.002 {
                    character.direction *= -1.0;
                }
            }
        }

        // Check for character meetings
        self.check_character_meetings();

        // Randomly start conversations
        if self.chat_timer > 3.0 && rng.gen_range(0.0..1.0) < 0.1 {
            self.try_start_conversation_with_weather(weather_data);
            self.chat_timer = 0.0;
        }
    }

    fn check_character_meetings(&mut self) {
        let mut meetings = Vec::new();

        for i in 0..self.characters.len() {
            for j in (i + 1)..self.characters.len() {
                let char1 = &self.characters[i];
                let char2 = &self.characters[j];

                // Check if characters are close enough and not already chatting
                let distance = (char1.x - char2.x).abs();
                if distance < 60.0 && !char1.is_chatting && !char2.is_chatting {
                    meetings.push((i, j));
                }
            }
        }

        // Start conversations for meeting characters
        for (i, j) in meetings {
            self.start_conversation_with_weather(i, j, weather_data);
        }
    }

    fn try_start_conversation_with_weather(&mut self, weather_data: Option<&crate::WeatherData>) {
        let mut rng = rand::thread_rng();
        let available_chars: Vec<usize> = self.characters
            .iter()
            .enumerate()
            .filter(|(_, c)| !c.is_chatting)
            .map(|(i, _)| i)
            .collect();

        if available_chars.len() >= 2 {
            let idx1 = available_chars[rng.gen_range(0..available_chars.len())];
            let idx2 = available_chars[rng.gen_range(0..available_chars.len())];

            if idx1 != idx2 {
                // Move characters closer if they're far apart
                let char1_x = self.characters[idx1].x;
                let char2_x = self.characters[idx2].x;

                if (char1_x - char2_x).abs() > 100.0 {
                    let meeting_point = (char1_x + char2_x) / 2.0;
                    self.characters[idx1].x = meeting_point - 30.0;
                    self.characters[idx2].x = meeting_point + 30.0;
                }

                self.start_conversation_with_weather(idx1, idx2, weather_data);
            }
        }
    }

    fn try_start_conversation(&mut self) {
        self.try_start_conversation_with_weather(None);
    }

    pub fn start_conversation_with_weather(&mut self, char1_idx: usize, char2_idx: usize, weather_data: Option<&crate::WeatherData>) {
        let mut rng = rand::thread_rng();

        // Set both characters as chatting
        self.characters[char1_idx].is_chatting = true;
        self.characters[char1_idx].chat_partner_id = Some(char2_idx);
        self.characters[char1_idx].chat_duration = 0.0;

        self.characters[char2_idx].is_chatting = true;
        self.characters[char2_idx].chat_partner_id = Some(char1_idx);
        self.characters[char2_idx].chat_duration = 0.0;

        // Get weather-appropriate messages
        let messages = if let Some(weather) = weather_data {
            let weather_type = weather.get_weather_type();
            self.get_weather_chat_messages(&weather_type, weather.temperature, weather.is_day)
        } else {
            // Default messages if no weather data
            vec![
                "Nice day, isn't it?",
                "How's it going?",
                "Lovely weather!",
                "Great day for a walk!",
            ]
        };

        let message1 = messages[rng.gen_range(0..messages.len())].to_string();
        let message2 = messages[rng.gen_range(0..messages.len())].to_string();

        self.chat_bubbles.push(ChatBubble {
            x: self.characters[char1_idx].x,
            y: self.characters[char1_idx].y - 40.0,
            message: message1,
            duration: 0.0,
            max_duration: 3.0,
            character_id: char1_idx,
            bubble_type: BubbleType::Normal,
        });

        // Delay second message slightly
        self.chat_bubbles.push(ChatBubble {
            x: self.characters[char2_idx].x,
            y: self.characters[char2_idx].y - 40.0,
            message: message2,
            duration: -1.0, // Start delayed
            max_duration: 3.0,
            character_id: char2_idx,
            bubble_type: BubbleType::Normal,
        });
    }

    fn start_conversation(&mut self, char1_idx: usize, char2_idx: usize) {
        // Fallback method for internal use without weather data
        self.start_conversation_with_weather(char1_idx, char2_idx, None);
    }

    pub fn get_weather_chat_messages(&self, weather_type: &crate::weather::WeatherType, temperature: f64, is_day: bool) -> Vec<&'static str> {
        match weather_type {
            crate::weather::WeatherType::Clear => {
                if is_day {
                    if temperature > 25.0 {
                        vec![
                            "Beautiful sunny day!",
                            "Perfect weather for a picnic!",
                            "Love this sunshine!",
                            "Great day to be outside!",
                            "Couldn't ask for better weather!",
                            "This heat feels amazing!",
                            "Perfect beach weather!",
                        ]
                    } else if temperature > 15.0 {
                        vec![
                            "Nice weather today!",
                            "Perfect day for a walk!",
                            "Love the clear skies!",
                            "Great day to be outside!",
                            "Beautiful day, isn't it?",
                            "Perfect temperature!",
                        ]
                    } else {
                        vec![
                            "Clear but a bit chilly!",
                            "At least it's sunny!",
                            "Crisp and clear today!",
                            "Cold but beautiful!",
                            "Need a jacket today!",
                        ]
                    }
                } else {
                    vec![
                        "Beautiful clear night!",
                        "Look at those stars!",
                        "Perfect evening for a walk!",
                        "Love these clear nights!",
                        "Great night to be out!",
                    ]
                }
            },
            crate::weather::WeatherType::PartlyCloudy => {
                vec![
                    "Nice mix of sun and clouds!",
                    "Perfect weather, not too hot!",
                    "Love these partly cloudy days!",
                    "Great weather for walking!",
                    "Just the right amount of clouds!",
                    "Comfortable weather today!",
                ]
            },
            crate::weather::WeatherType::Rain | crate::weather::WeatherType::RainShowers => {
                vec![
                    "Getting soaked out here!",
                    "Forgot my umbrella again!",
                    "This rain is heavy!",
                    "Hope it stops soon!",
                    "At least the plants are happy!",
                    "Good day to stay inside!",
                    "This weather is depressing...",
                    "Wish I had an umbrella!",
                    "Rain, rain, go away!",
                ]
            },
            crate::weather::WeatherType::Snow | crate::weather::WeatherType::SnowShowers => {
                vec![
                    "It's snowing!",
                    "Winter wonderland out here!",
                    "Hope the roads aren't icy!",
                    "Beautiful snowfall!",
                    "Time to build a snowman!",
                    "Slippery out here!",
                    "Love the snow!",
                    "Winter is here!",
                    "So cold and snowy!",
                ]
            },
            crate::weather::WeatherType::Thunderstorm => {
                vec![
                    "This storm is scary!",
                    "That lightning was close!",
                    "We should get inside!",
                    "This thunder is loud!",
                    "Dangerous weather today!",
                    "Hope the power doesn't go out!",
                    "This storm came out of nowhere!",
                    "Better find shelter!",
                    "Wild weather today!",
                ]
            },
            crate::weather::WeatherType::Fog => {
                vec![
                    "Can barely see anything!",
                    "This fog is thick!",
                    "Spooky weather today!",
                    "Hope it clears up soon!",
                    "Driving must be dangerous!",
                    "Like walking in a cloud!",
                    "Mysterious weather!",
                ]
            },
            crate::weather::WeatherType::Drizzle => {
                vec![
                    "Just a light drizzle!",
                    "Misty weather today!",
                    "Not too bad, just damp!",
                    "Could be worse!",
                    "Light rain, no big deal!",
                    "Refreshing drizzle!",
                ]
            },
        }
    }

    fn update_chat_bubbles(&mut self, delta_time: f64) {
        // Update existing bubbles
        for bubble in &mut self.chat_bubbles {
            bubble.duration += delta_time;

            // Update position to follow character
            if bubble.character_id < self.characters.len() {
                bubble.x = self.characters[bubble.character_id].x;
                bubble.y = self.characters[bubble.character_id].y - 40.0;
            }
        }

        // Remove expired bubbles
        self.chat_bubbles.retain(|bubble| bubble.duration < bubble.max_duration);
    }
}

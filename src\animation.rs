use rand::Rng;

pub struct AnimationSystem {
    pub cloud_offset: f64,
    pub rain_offset: f64,
    pub snow_offset: f64,
    pub birds: Vec<Bird>,
    pub characters: Vec<Character>,
    pub chat_bubbles: Vec<ChatBubble>,
    pub cars: Vec<Car>,
    lightning_timer: f64,
    lightning_frequency: f64,
    chat_timer: f64,
    car_spawn_timer: f64,
    current_speaker: Option<usize>, // Track who is currently speaking
    speaker_end_time: f64, // When current speaker will finish
}

pub struct Bird {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub wing_phase: f64,
    pub wing_speed: f64,
}

pub struct Car {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub direction: f64, // -1 for left, 1 for right
    pub car_type: CarType,
    pub color: CarColor,
}

pub struct Character {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub direction: f64, // -1 for left, 1 for right
    pub walk_phase: f64,
    pub character_type: CharacterType,
    pub is_chatting: bool,
    pub chat_partner_id: Option<usize>,
    pub chat_duration: f64,
    pub idle_timer: f64,
}

pub struct ChatBubble {
    pub x: f64,
    pub y: f64,
    pub message: String,
    pub duration: f64,
    pub max_duration: f64,
    pub character_id: usize,
    pub bubble_type: BubbleType,
}

#[derive(Clone, Copy)]
pub enum CharacterType {
    Person,
    Dog,
    Child,
    Elder,
}



#[derive(Clone, Copy)]
pub enum BubbleType {
    Normal,
    Angry,
    Fighting,
}

#[derive(Clone, Copy)]
pub enum CarType {
    Sedan,
    SUV,
    Truck,
    Compact,
}

#[derive(Clone, Copy)]
pub enum CarColor {
    Red,
    Blue,
    White,
    Black,
    Silver,
    Green,
}

impl AnimationSystem {
    pub fn new() -> Self {
        let mut rng = rand::thread_rng();
        let mut birds = Vec::new();
        let mut characters = Vec::new();

        // Create some birds with realistic speeds
        for _ in 0..5 {
            birds.push(Bird {
                x: rng.gen_range(0.0..800.0),
                y: rng.gen_range(50.0..200.0),
                speed: rng.gen_range(8.0..15.0), // Much slower, realistic bird speed
                wing_phase: rng.gen_range(0.0..1.0),
                wing_speed: rng.gen_range(2.0..4.0), // Slower wing flapping
            });
        }

        // Create more characters for a livelier scene
        for _ in 0..8 { // Increased from 6 to 8
            let character_types = [CharacterType::Person, CharacterType::Dog, CharacterType::Child, CharacterType::Elder];
            characters.push(Character {
                x: rng.gen_range(0.0..800.0),
                y: 0.0, // Will be set based on ground level
                speed: rng.gen_range(25.0..50.0), // Much faster walking speeds
                direction: if rng.gen_bool(0.5) { 1.0 } else { -1.0 },
                walk_phase: rng.gen_range(0.0..1.0),
                character_type: character_types[rng.gen_range(0..character_types.len())],
                is_chatting: false,
                chat_partner_id: None,
                chat_duration: 0.0,
                idle_timer: 0.0,
            });
        }

        Self {
            cloud_offset: 0.0,
            rain_offset: 0.0,
            snow_offset: 0.0,
            birds,
            characters,
            chat_bubbles: Vec::new(),
            cars: Vec::new(),
            lightning_timer: 0.0,
            lightning_frequency: 3.0, // Lightning every 3 seconds on average
            chat_timer: 0.0,
            car_spawn_timer: 0.0,
            current_speaker: None,
            speaker_end_time: 0.0,
        }
    }

    pub fn update(&mut self, delta_time: f64) {
        self.update_with_weather(delta_time, None);
    }

    pub fn update_with_weather(&mut self, delta_time: f64, weather_data: Option<&crate::WeatherData>) {
        let timestamp = js_sys::Date::now();
        // Update cloud movement - much slower, realistic speed
        self.cloud_offset += delta_time * 2.0; // Very slow cloud movement

        // Update precipitation
        self.rain_offset += delta_time * 200.0; // Fast rain drops (this is fine)
        self.snow_offset += delta_time * 30.0;  // Slower snow

        // Update birds - much slower, realistic speed
        for bird in &mut self.birds {
            bird.x += bird.speed * delta_time;
            bird.wing_phase += bird.wing_speed * delta_time;

            // Wrap birds around screen
            if bird.x > 900.0 {
                bird.x = -100.0;
                // Randomize height when bird wraps
                let mut rng = rand::thread_rng();
                bird.y = rng.gen_range(50.0..200.0);
            }
        }

        // Update characters with weather context
        self.update_characters_with_weather(delta_time, weather_data, timestamp);

        // Update cars
        self.update_cars(delta_time);

        // Update chat bubbles
        self.update_chat_bubbles(delta_time);

        // Update lightning timer
        self.lightning_timer += delta_time;

        // Update chat timer
        self.chat_timer += delta_time;

        // Update car spawn timer
        self.car_spawn_timer += delta_time;
    }

    pub fn should_show_lightning(&mut self) -> bool {
        let mut rng = rand::thread_rng();
        if self.lightning_timer > self.lightning_frequency {
            self.lightning_timer = 0.0;
            self.lightning_frequency = rng.gen_range(2.0..5.0); // Random interval
            rng.gen_bool(0.3) // 30% chance to show lightning
        } else {
            false
        }
    }

    fn update_characters_with_weather(&mut self, delta_time: f64, weather_data: Option<&crate::WeatherData>, timestamp: f64) {
        let ground_y = 520.0; // Foreground level - people walk in front of road
        let mut rng = rand::thread_rng();

        // Update each character
        for i in 0..self.characters.len() {
            let character = &mut self.characters[i];
            character.y = ground_y;

            if character.is_chatting {
                // Always keep walking, even while chatting
                character.x += character.direction * character.speed * delta_time * 0.5; // Half speed while talking
                character.walk_phase += delta_time * 3.0; // Continue walking animation

                // Handle chatting behavior - force end conversations quickly
                character.chat_duration += delta_time;
                if character.chat_duration > 2.0 { // Longer chats - 2 seconds minimum
                    // Force end conversation immediately
                    character.is_chatting = false;
                    character.chat_partner_id = None;
                    character.chat_duration = 0.0;
                    character.idle_timer = 0.0;

                    // Change direction to walk away
                    character.direction *= -1.0;
                }
            } else if character.idle_timer > 0.0 {
                // Brief idle period after chatting
                character.idle_timer -= delta_time;
            } else {
                // Normal walking behavior - much more active
                character.x += character.direction * character.speed * delta_time;
                character.walk_phase += delta_time * 5.0; // Faster walking animation

                // Wrap around screen
                if character.x > 850.0 {
                    character.x = -50.0;
                    character.direction = 1.0;
                } else if character.x < -50.0 {
                    character.x = 850.0;
                    character.direction = -1.0;
                }

                // More frequent direction changes for more dynamic movement
                if rng.gen_range(0.0..1.0) < 0.008 { // 4x more likely to change direction
                    character.direction *= -1.0;
                }

                // Occasionally speed up or slow down
                if rng.gen_range(0.0..1.0) < 0.001 {
                    character.speed = rng.gen_range(20.0..45.0); // Vary walking speed
                }
            }
        }

        // Disable automatic meetings - only allow very rare random conversations
        // self.check_character_meetings(weather_data);

        // Check if current speaker has finished
        if let Some(_speaker_id) = self.current_speaker {
            if timestamp > self.speaker_end_time {
                // Current speaker is done, clear the speaker
                self.current_speaker = None;
                self.speaker_end_time = 0.0;
            }
        }

        // Only start new conversations very rarely - much longer intervals
        if self.current_speaker.is_none() && self.chat_timer > 60.0 && rng.gen_range(0.0..1.0) < 0.001 {
            self.try_start_conversation_with_weather(weather_data, timestamp);
            self.chat_timer = 0.0;
        }
    }

    fn check_character_meetings(&mut self, weather_data: Option<&crate::WeatherData>) {
        let mut meetings = Vec::new();

        for i in 0..self.characters.len() {
            for j in (i + 1)..self.characters.len() {
                let char1 = &self.characters[i];
                let char2 = &self.characters[j];

                // Check if characters are very close and not already chatting (exclude dogs)
                let distance = (char1.x - char2.x).abs();
                if distance < 25.0 && !char1.is_chatting && !char2.is_chatting
                    && !matches!(char1.character_type, CharacterType::Dog)
                    && !matches!(char2.character_type, CharacterType::Dog) {
                    meetings.push((i, j));
                }
            }
        }

        // Start conversations for meeting characters
        for (i, j) in meetings {
            self.start_conversation_with_weather(i, j, weather_data);
        }
    }

    fn try_start_conversation_with_weather(&mut self, weather_data: Option<&crate::WeatherData>, _timestamp: f64) {
        let mut rng = rand::thread_rng();
        let available_chars: Vec<usize> = self.characters
            .iter()
            .enumerate()
            .filter(|(_, c)| !c.is_chatting && !matches!(c.character_type, CharacterType::Dog))
            .map(|(i, _)| i)
            .collect();

        // Only allow conversations if we have at least 5 people and pick randomly from a small subset
        if available_chars.len() >= 5 {
            // Only pick from first 20% of available characters (1 in 5 rule)
            let max_participants = (available_chars.len() / 5).max(2);
            let subset: Vec<usize> = available_chars.into_iter().take(max_participants).collect();

            if subset.len() >= 2 {
                let idx1 = subset[rng.gen_range(0..subset.len())];
                let idx2 = subset[rng.gen_range(0..subset.len())];

                if idx1 != idx2 {
                    // Move characters closer if they're far apart
                    let char1_x = self.characters[idx1].x;
                    let char2_x = self.characters[idx2].x;

                    if (char1_x - char2_x).abs() > 100.0 {
                        let meeting_point = (char1_x + char2_x) / 2.0;
                        self.characters[idx1].x = meeting_point - 30.0;
                        self.characters[idx2].x = meeting_point + 30.0;
                    }

                    self.start_conversation_with_weather(idx1, idx2, weather_data);
                }
            }
        }
    }

    fn try_start_conversation(&mut self) {
        let timestamp = js_sys::Date::now();
        self.try_start_conversation_with_weather(None, timestamp);
    }

    pub fn start_conversation_with_weather(&mut self, char1_idx: usize, char2_idx: usize, weather_data: Option<&crate::WeatherData>) {
        let mut rng = rand::thread_rng();

        // Set both characters as chatting
        self.characters[char1_idx].is_chatting = true;
        self.characters[char1_idx].chat_partner_id = Some(char2_idx);
        self.characters[char1_idx].chat_duration = 0.0;

        self.characters[char2_idx].is_chatting = true;
        self.characters[char2_idx].chat_partner_id = Some(char1_idx);
        self.characters[char2_idx].chat_duration = 0.0;

        // Get weather-appropriate messages
        let messages = if let Some(weather) = weather_data {
            let weather_type = weather.get_weather_type();
            self.get_weather_chat_messages(&weather_type, weather.temperature, weather.is_day)
        } else {
            // Default messages if no weather data
            vec![
                "Nice day, isn't it?",
                "How's it going?",
                "Lovely weather!",
                "Great day for a walk!",
            ]
        };

        let message = messages[rng.gen_range(0..messages.len())].to_string();

        // Only one person talks at a time
        let speaker_idx = if rng.gen_bool(0.5) { char1_idx } else { char2_idx };

        // Set current speaker and duration - longer speech bubbles
        self.current_speaker = Some(speaker_idx);
        self.speaker_end_time = js_sys::Date::now() + 2000.0; // 2 seconds from now

        self.chat_bubbles.push(ChatBubble {
            x: self.characters[speaker_idx].x,
            y: self.characters[speaker_idx].y - 40.0,
            message,
            duration: 0.0,
            max_duration: 2.0, // Show bubble for 2 seconds
            character_id: speaker_idx,
            bubble_type: BubbleType::Normal,
        });
    }

    fn start_conversation(&mut self, char1_idx: usize, char2_idx: usize) {
        // Fallback method for internal use without weather data
        self.start_conversation_with_weather(char1_idx, char2_idx, None);
    }

    pub fn get_weather_chat_messages(&self, weather_type: &crate::weather::WeatherType, temperature: f64, is_day: bool) -> Vec<&'static str> {
        match weather_type {
            crate::weather::WeatherType::Clear => {
                if is_day {
                    if temperature > 25.0 {
                        vec![
                            "Beautiful sunny day!",
                            "Perfect weather for a picnic!",
                            "Love this sunshine!",
                            "Great day to be outside!",
                            "Couldn't ask for better weather!",
                            "This heat feels amazing!",
                            "Perfect beach weather!",
                        ]
                    } else if temperature > 15.0 {
                        vec![
                            "Nice weather today!",
                            "Perfect day for a walk!",
                            "Love the clear skies!",
                            "Great day to be outside!",
                            "Beautiful day, isn't it?",
                            "Perfect temperature!",
                        ]
                    } else {
                        vec![
                            "Clear but a bit chilly!",
                            "At least it's sunny!",
                            "Crisp and clear today!",
                            "Cold but beautiful!",
                            "Need a jacket today!",
                        ]
                    }
                } else {
                    vec![
                        "Beautiful clear night!",
                        "Look at those stars!",
                        "Perfect evening for a walk!",
                        "Love these clear nights!",
                        "Great night to be out!",
                    ]
                }
            },
            crate::weather::WeatherType::PartlyCloudy => {
                vec![
                    "Nice mix of sun and clouds!",
                    "Perfect weather, not too hot!",
                    "Love these partly cloudy days!",
                    "Great weather for walking!",
                    "Just the right amount of clouds!",
                    "Comfortable weather today!",
                ]
            },
            crate::weather::WeatherType::Rain | crate::weather::WeatherType::RainShowers => {
                vec![
                    "Getting soaked out here!",
                    "Forgot my umbrella again!",
                    "This rain is heavy!",
                    "Hope it stops soon!",
                    "At least the plants are happy!",
                    "Good day to stay inside!",
                    "This weather is depressing...",
                    "Wish I had an umbrella!",
                    "Rain, rain, go away!",
                ]
            },
            crate::weather::WeatherType::Snow | crate::weather::WeatherType::SnowShowers => {
                vec![
                    "It's snowing!",
                    "Winter wonderland out here!",
                    "Hope the roads aren't icy!",
                    "Beautiful snowfall!",
                    "Time to build a snowman!",
                    "Slippery out here!",
                    "Love the snow!",
                    "Winter is here!",
                    "So cold and snowy!",
                ]
            },
            crate::weather::WeatherType::Thunderstorm => {
                vec![
                    "This storm is scary!",
                    "That lightning was close!",
                    "We should get inside!",
                    "This thunder is loud!",
                    "Dangerous weather today!",
                    "Hope the power doesn't go out!",
                    "This storm came out of nowhere!",
                    "Better find shelter!",
                    "Wild weather today!",
                ]
            },
            crate::weather::WeatherType::Fog => {
                vec![
                    "Can barely see anything!",
                    "This fog is thick!",
                    "Spooky weather today!",
                    "Hope it clears up soon!",
                    "Driving must be dangerous!",
                    "Like walking in a cloud!",
                    "Mysterious weather!",
                ]
            },
            crate::weather::WeatherType::Drizzle => {
                vec![
                    "Just a light drizzle!",
                    "Misty weather today!",
                    "Not too bad, just damp!",
                    "Could be worse!",
                    "Light rain, no big deal!",
                    "Refreshing drizzle!",
                ]
            },
        }
    }

    fn update_chat_bubbles(&mut self, delta_time: f64) {
        // Update existing bubbles
        // Cleanup conversations after reasonable time
        for character in &mut self.characters {
            if character.is_chatting && character.chat_duration > 3.0 {
                // Force end conversations after 3 seconds max
                character.is_chatting = false;
                character.chat_partner_id = None;
                character.chat_duration = 0.0;
                character.idle_timer = 0.0;
                // Change direction to walk away
                character.direction *= -1.0;
            }
        }

        for bubble in &mut self.chat_bubbles {
            bubble.duration += delta_time;

            // Update position to follow character
            if bubble.character_id < self.characters.len() {
                bubble.x = self.characters[bubble.character_id].x;
                bubble.y = self.characters[bubble.character_id].y - 40.0;
            }
        }

        // Remove expired bubbles
        self.chat_bubbles.retain(|bubble| bubble.duration < bubble.max_duration);
    }

    fn update_cars(&mut self, delta_time: f64) {
        let mut rng = rand::thread_rng();

        // Update existing cars
        for car in &mut self.cars {
            car.x += car.direction * car.speed * delta_time;
        }

        // Remove cars that have gone off screen
        self.cars.retain(|car| {
            if car.direction > 0.0 {
                car.x < 900.0 // Going right
            } else {
                car.x > -100.0 // Going left
            }
        });

        // Spawn new cars occasionally
        if self.car_spawn_timer > 8.0 && rng.gen_range(0.0..1.0) < 0.3 { // Every 8+ seconds, 30% chance
            self.spawn_car();
            self.car_spawn_timer = 0.0;
        }
    }

    fn spawn_car(&mut self) {
        let mut rng = rand::thread_rng();
        let car_types = [CarType::Sedan, CarType::SUV, CarType::Truck, CarType::Compact];
        let car_colors = [CarColor::Red, CarColor::Blue, CarColor::White, CarColor::Black, CarColor::Silver, CarColor::Green];

        let direction = if rng.gen_bool(0.5) { 1.0 } else { -1.0 };
        let start_x = if direction > 0.0 { -80.0 } else { 880.0 };

        self.cars.push(Car {
            x: start_x,
            y: 440.0, // On the road center line
            speed: rng.gen_range(80.0..120.0), // Faster than people
            direction,
            car_type: car_types[rng.gen_range(0..car_types.len())],
            color: car_colors[rng.gen_range(0..car_colors.len())],
        });
    }


}

use rand::Rng;

pub struct AnimationSystem {
    pub cloud_offset: f64,
    pub rain_offset: f64,
    pub snow_offset: f64,
    pub birds: Vec<Bird>,
    pub characters: Vec<Character>,
    pub chat_bubbles: Vec<ChatBubble>,
    lightning_timer: f64,
    lightning_frequency: f64,
    chat_timer: f64,
}

pub struct Bird {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub wing_phase: f64,
    pub wing_speed: f64,
}

pub struct Character {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub direction: f64, // -1 for left, 1 for right
    pub walk_phase: f64,
    pub character_type: CharacterType,
    pub is_chatting: bool,
    pub chat_partner_id: Option<usize>,
    pub chat_duration: f64,
    pub idle_timer: f64,
}

pub struct ChatBubble {
    pub x: f64,
    pub y: f64,
    pub message: String,
    pub duration: f64,
    pub max_duration: f64,
    pub character_id: usize,
}

#[derive(<PERSON><PERSON>, Copy)]
pub enum CharacterType {
    <PERSON>,
    <PERSON>,
    <PERSON>,
    <PERSON>,
}

impl AnimationSystem {
    pub fn new() -> Self {
        let mut rng = rand::thread_rng();
        let mut birds = Vec::new();
        let mut characters = Vec::new();

        // Create some birds
        for _ in 0..5 {
            birds.push(Bird {
                x: rng.gen_range(0.0..800.0),
                y: rng.gen_range(50.0..200.0),
                speed: rng.gen_range(20.0..50.0),
                wing_phase: rng.gen_range(0.0..1.0),
                wing_speed: rng.gen_range(3.0..6.0),
            });
        }

        // Create some characters
        for _ in 0..6 {
            let character_types = [CharacterType::Person, CharacterType::Dog, CharacterType::Child, CharacterType::Elder];
            characters.push(Character {
                x: rng.gen_range(0.0..800.0),
                y: 0.0, // Will be set based on ground level
                speed: rng.gen_range(15.0..35.0),
                direction: if rng.gen_bool(0.5) { 1.0 } else { -1.0 },
                walk_phase: rng.gen_range(0.0..1.0),
                character_type: character_types[rng.gen_range(0..character_types.len())],
                is_chatting: false,
                chat_partner_id: None,
                chat_duration: 0.0,
                idle_timer: 0.0,
            });
        }

        Self {
            cloud_offset: 0.0,
            rain_offset: 0.0,
            snow_offset: 0.0,
            birds,
            characters,
            chat_bubbles: Vec::new(),
            lightning_timer: 0.0,
            lightning_frequency: 3.0, // Lightning every 3 seconds on average
            chat_timer: 0.0,
        }
    }

    pub fn update(&mut self, delta_time: f64) {
        // Update cloud movement
        self.cloud_offset += delta_time * 10.0; // Slow cloud movement

        // Update precipitation
        self.rain_offset += delta_time * 200.0; // Fast rain drops
        self.snow_offset += delta_time * 50.0;  // Slower snow

        // Update birds
        for bird in &mut self.birds {
            bird.x += bird.speed * delta_time;
            bird.wing_phase += bird.wing_speed * delta_time;

            // Wrap birds around screen
            if bird.x > 900.0 {
                bird.x = -100.0;
                // Randomize height when bird wraps
                let mut rng = rand::thread_rng();
                bird.y = rng.gen_range(50.0..200.0);
            }
        }

        // Update characters
        self.update_characters(delta_time);

        // Update chat bubbles
        self.update_chat_bubbles(delta_time);

        // Update lightning timer
        self.lightning_timer += delta_time;

        // Update chat timer
        self.chat_timer += delta_time;
    }

    pub fn should_show_lightning(&mut self) -> bool {
        let mut rng = rand::thread_rng();
        if self.lightning_timer > self.lightning_frequency {
            self.lightning_timer = 0.0;
            self.lightning_frequency = rng.gen_range(2.0..5.0); // Random interval
            rng.gen_bool(0.3) // 30% chance to show lightning
        } else {
            false
        }
    }

    fn update_characters(&mut self, delta_time: f64) {
        let ground_y = 420.0; // Ground level
        let mut rng = rand::thread_rng();

        // Update each character
        for i in 0..self.characters.len() {
            let character = &mut self.characters[i];
            character.y = ground_y;

            if character.is_chatting {
                // Handle chatting behavior
                character.chat_duration += delta_time;
                if character.chat_duration > 5.0 { // Chat for 5 seconds
                    character.is_chatting = false;
                    character.chat_partner_id = None;
                    character.chat_duration = 0.0;
                    character.idle_timer = rng.gen_range(2.0..5.0); // Idle before moving
                }
            } else if character.idle_timer > 0.0 {
                // Idle period after chatting
                character.idle_timer -= delta_time;
            } else {
                // Normal walking behavior
                character.x += character.direction * character.speed * delta_time;
                character.walk_phase += delta_time * 4.0;

                // Wrap around screen
                if character.x > 850.0 {
                    character.x = -50.0;
                    character.direction = 1.0;
                } else if character.x < -50.0 {
                    character.x = 850.0;
                    character.direction = -1.0;
                }

                // Randomly change direction
                if rng.gen_range(0.0..1.0) < 0.002 {
                    character.direction *= -1.0;
                }
            }
        }

        // Check for character meetings
        self.check_character_meetings();

        // Randomly start conversations
        if self.chat_timer > 3.0 && rng.gen_range(0.0..1.0) < 0.1 {
            self.try_start_conversation();
            self.chat_timer = 0.0;
        }
    }

    fn check_character_meetings(&mut self) {
        let mut meetings = Vec::new();

        for i in 0..self.characters.len() {
            for j in (i + 1)..self.characters.len() {
                let char1 = &self.characters[i];
                let char2 = &self.characters[j];

                // Check if characters are close enough and not already chatting
                let distance = (char1.x - char2.x).abs();
                if distance < 60.0 && !char1.is_chatting && !char2.is_chatting {
                    meetings.push((i, j));
                }
            }
        }

        // Start conversations for meeting characters
        for (i, j) in meetings {
            self.start_conversation(i, j);
        }
    }

    fn try_start_conversation(&mut self) {
        let mut rng = rand::thread_rng();
        let available_chars: Vec<usize> = self.characters
            .iter()
            .enumerate()
            .filter(|(_, c)| !c.is_chatting)
            .map(|(i, _)| i)
            .collect();

        if available_chars.len() >= 2 {
            let idx1 = available_chars[rng.gen_range(0..available_chars.len())];
            let idx2 = available_chars[rng.gen_range(0..available_chars.len())];

            if idx1 != idx2 {
                // Move characters closer if they're far apart
                let char1_x = self.characters[idx1].x;
                let char2_x = self.characters[idx2].x;

                if (char1_x - char2_x).abs() > 100.0 {
                    let meeting_point = (char1_x + char2_x) / 2.0;
                    self.characters[idx1].x = meeting_point - 30.0;
                    self.characters[idx2].x = meeting_point + 30.0;
                }

                self.start_conversation(idx1, idx2);
            }
        }
    }

    fn start_conversation(&mut self, char1_idx: usize, char2_idx: usize) {
        let mut rng = rand::thread_rng();

        // Set both characters as chatting
        self.characters[char1_idx].is_chatting = true;
        self.characters[char1_idx].chat_partner_id = Some(char2_idx);
        self.characters[char1_idx].chat_duration = 0.0;

        self.characters[char2_idx].is_chatting = true;
        self.characters[char2_idx].chat_partner_id = Some(char1_idx);
        self.characters[char2_idx].chat_duration = 0.0;

        // Create chat bubbles
        let messages = self.get_weather_chat_messages();
        let message1 = messages[rng.gen_range(0..messages.len())].to_string();
        let message2 = messages[rng.gen_range(0..messages.len())].to_string();

        self.chat_bubbles.push(ChatBubble {
            x: self.characters[char1_idx].x,
            y: self.characters[char1_idx].y - 40.0,
            message: message1,
            duration: 0.0,
            max_duration: 3.0,
            character_id: char1_idx,
        });

        // Delay second message slightly
        self.chat_bubbles.push(ChatBubble {
            x: self.characters[char2_idx].x,
            y: self.characters[char2_idx].y - 40.0,
            message: message2,
            duration: -1.0, // Start delayed
            max_duration: 3.0,
            character_id: char2_idx,
        });
    }

    fn get_weather_chat_messages(&self) -> Vec<&'static str> {
        vec![
            "Nice weather today!",
            "Looks like rain...",
            "Beautiful sunset!",
            "Bit chilly, isn't it?",
            "Love this sunshine!",
            "Hope it doesn't storm",
            "Perfect day for a walk",
            "Weather's been crazy",
            "At least it's not snowing",
            "Great day to be outside!",
            "Feels like spring!",
            "Could use some rain",
            "What a lovely day",
            "Bit windy today",
            "Perfect temperature!",
        ]
    }

    fn update_chat_bubbles(&mut self, delta_time: f64) {
        // Update existing bubbles
        for bubble in &mut self.chat_bubbles {
            bubble.duration += delta_time;

            // Update position to follow character
            if bubble.character_id < self.characters.len() {
                bubble.x = self.characters[bubble.character_id].x;
                bubble.y = self.characters[bubble.character_id].y - 40.0;
            }
        }

        // Remove expired bubbles
        self.chat_bubbles.retain(|bubble| bubble.duration < bubble.max_duration);
    }
}

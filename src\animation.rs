use rand::Rng;

pub struct AnimationSystem {
    pub cloud_offset: f64,
    pub rain_offset: f64,
    pub snow_offset: f64,
    pub birds: Vec<Bird>,
    lightning_timer: f64,
    lightning_frequency: f64,
}

pub struct Bird {
    pub x: f64,
    pub y: f64,
    pub speed: f64,
    pub wing_phase: f64,
    pub wing_speed: f64,
}

impl AnimationSystem {
    pub fn new() -> Self {
        let mut rng = rand::thread_rng();
        let mut birds = Vec::new();
        
        // Create some birds
        for _ in 0..5 {
            birds.push(Bird {
                x: rng.gen_range(0.0..800.0),
                y: rng.gen_range(50.0..200.0),
                speed: rng.gen_range(20.0..50.0),
                wing_phase: rng.gen_range(0.0..1.0),
                wing_speed: rng.gen_range(3.0..6.0),
            });
        }

        Self {
            cloud_offset: 0.0,
            rain_offset: 0.0,
            snow_offset: 0.0,
            birds,
            lightning_timer: 0.0,
            lightning_frequency: 3.0, // Lightning every 3 seconds on average
        }
    }

    pub fn update(&mut self, delta_time: f64) {
        // Update cloud movement
        self.cloud_offset += delta_time * 10.0; // Slow cloud movement
        
        // Update precipitation
        self.rain_offset += delta_time * 200.0; // Fast rain drops
        self.snow_offset += delta_time * 50.0;  // Slower snow
        
        // Update birds
        for bird in &mut self.birds {
            bird.x += bird.speed * delta_time;
            bird.wing_phase += bird.wing_speed * delta_time;
            
            // Wrap birds around screen
            if bird.x > 900.0 {
                bird.x = -100.0;
                // Randomize height when bird wraps
                let mut rng = rand::thread_rng();
                bird.y = rng.gen_range(50.0..200.0);
            }
        }
        
        // Update lightning timer
        self.lightning_timer += delta_time;
    }

    pub fn should_show_lightning(&mut self) -> bool {
        let mut rng = rand::thread_rng();
        if self.lightning_timer > self.lightning_frequency {
            self.lightning_timer = 0.0;
            self.lightning_frequency = rng.gen_range(2.0..5.0); // Random interval
            rng.gen_bool(0.3) // 30% chance to show lightning
        } else {
            false
        }
    }
}

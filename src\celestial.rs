use js_sys::Date;

pub struct CelestialBodies {
    pub is_day: bool,
    sun_angle: f64,
    moon_angle: f64,
    time_of_day: f64, // 0.0 to 1.0, where 0.5 is noon
}

impl CelestialBodies {
    pub fn new() -> Self {
        Self {
            is_day: true,
            sun_angle: 0.0,
            moon_angle: 0.0,
            time_of_day: 0.5,
        }
    }

    pub fn update(&mut self, timestamp: f64) {
        // Convert timestamp to time of day (0.0 to 1.0)
        let date = Date::new(&timestamp.into());
        let hours = date.get_hours() as f64;
        let minutes = date.get_minutes() as f64;
        let seconds = date.get_seconds() as f64;
        
        self.time_of_day = (hours + minutes / 60.0 + seconds / 3600.0) / 24.0;
        
        // Determine if it's day or night (6 AM to 6 PM is day)
        let hour_of_day = self.time_of_day * 24.0;
        self.is_day = hour_of_day >= 6.0 && hour_of_day < 18.0;
        
        // Calculate sun angle (peaks at noon)
        self.sun_angle = if self.is_day {
            // Sun moves from east (0°) to west (180°) during day
            let day_progress = (hour_of_day - 6.0) / 12.0; // 0.0 to 1.0 during day
            day_progress * std::f64::consts::PI
        } else {
            0.0
        };
        
        // Calculate moon angle (opposite of sun, roughly)
        self.moon_angle = if !self.is_day {
            let night_progress = if hour_of_day < 6.0 {
                (hour_of_day + 6.0) / 12.0 // Early morning
            } else {
                (hour_of_day - 18.0) / 12.0 // Evening
            };
            night_progress * std::f64::consts::PI
        } else {
            0.0
        };
    }

    pub fn get_sun_position(&self, screen_width: f64, screen_height: f64) -> (f64, f64) {
        let x = screen_width * 0.1 + (screen_width * 0.8) * (self.sun_angle / std::f64::consts::PI);
        let y = screen_height * 0.1 + (screen_height * 0.4) * (1.0 - self.sun_angle.sin());
        (x, y)
    }

    pub fn get_moon_position(&self, screen_width: f64, screen_height: f64) -> (f64, f64) {
        let x = screen_width * 0.1 + (screen_width * 0.8) * (self.moon_angle / std::f64::consts::PI);
        let y = screen_height * 0.1 + (screen_height * 0.4) * (1.0 - self.moon_angle.sin());
        (x, y)
    }

    pub fn get_sky_colors(&self) -> (String, String) {
        if self.is_day {
            let sun_height = self.sun_angle.sin();
            
            if sun_height > 0.8 {
                // High noon - bright blue sky
                ("rgb(135, 206, 250)".to_string(), "rgb(176, 224, 230)".to_string())
            } else if sun_height > 0.3 {
                // Day time - blue sky
                ("rgb(100, 149, 237)".to_string(), "rgb(176, 196, 222)".to_string())
            } else {
                // Sunrise/sunset - orange/pink sky
                ("rgb(255, 94, 77)".to_string(), "rgb(255, 154, 0)".to_string())
            }
        } else {
            // Night time - dark blue to black
            let moon_brightness = if self.moon_angle > 0.0 { self.moon_angle.sin() } else { 0.0 };
            let lightness = (moon_brightness * 30.0) as u8;
            (
                format!("rgb({}, {}, {})", lightness, lightness, lightness + 20),
                format!("rgb({}, {}, {})", lightness + 10, lightness + 10, lightness + 30)
            )
        }
    }
}

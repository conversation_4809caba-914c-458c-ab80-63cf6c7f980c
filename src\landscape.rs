use rand::Rng;

pub struct Landscape {
    pub terrain_heights: Vec<f64>,
    width: f64,
    height: f64,
}

impl Landscape {
    pub fn new(width: f64, height: f64) -> Self {
        let num_points = 50;
        let mut terrain_heights = Vec::with_capacity(num_points);
        
        // Generate terrain using simple noise
        let mut rng = rand::thread_rng();
        let mut current_height = 0.3;
        
        for i in 0..num_points {
            // Add some randomness but keep it smooth
            let variation = rng.gen_range(-0.05..0.05);
            current_height += variation;
            
            // Keep heights within reasonable bounds
            current_height = current_height.clamp(0.1, 0.6);
            
            // Add some larger features occasionally
            if i % 10 == 0 {
                current_height += rng.gen_range(-0.1..0.2);
                current_height = current_height.clamp(0.1, 0.6);
            }
            
            terrain_heights.push(current_height);
        }
        
        // Smooth the terrain
        Self::smooth_terrain(&mut terrain_heights);
        
        Self {
            terrain_heights,
            width,
            height,
        }
    }

    pub fn resize(&mut self, width: f64, height: f64) {
        self.width = width;
        self.height = height;
    }

    fn smooth_terrain(heights: &mut Vec<f64>) {
        let original = heights.clone();
        for i in 1..heights.len() - 1 {
            heights[i] = (original[i - 1] + original[i] * 2.0 + original[i + 1]) / 4.0;
        }
    }
}

use wasm_bindgen::prelude::*;
use web_sys::{CanvasRenderingContext2d, HtmlCanvasElement};
use js_sys::Date;

mod weather;
mod renderer;
mod landscape;
mod celestial;
mod animation;

use weather::WeatherData;
use renderer::Renderer;
use landscape::Landscape;
use celestial::CelestialBodies;
use animation::AnimationSystem;

// When the `wee_alloc` feature is enabled, use `wee_alloc` as the global allocator.
#[cfg(feature = "wee_alloc")]
#[global_allocator]
static ALLOC: wee_alloc::WeeAlloc = wee_alloc::WeeAlloc::INIT;

#[wasm_bindgen]
extern "C" {
    fn alert(s: &str);
    
    #[wasm_bindgen(js_namespace = console)]
    fn log(s: &str);
}

macro_rules! console_log {
    ($($t:tt)*) => (log(&format_args!($($t)*).to_string()))
}

#[wasm_bindgen]
pub struct WeatherApp {
    canvas: HtmlCanvasElement,
    context: CanvasRenderingContext2d,
    renderer: Renderer,
    landscape: Landscape,
    celestial: CelestialBodies,
    animation: AnimationSystem,
    weather_data: Option<WeatherData>,
    last_update: f64,
}

#[wasm_bindgen]
impl WeatherApp {
    #[wasm_bindgen(constructor)]
    pub fn new(canvas: HtmlCanvasElement) -> Result<WeatherApp, JsValue> {
        let context = canvas
            .get_context("2d")?
            .unwrap()
            .dyn_into::<CanvasRenderingContext2d>()?;

        let width = canvas.width() as f64;
        let height = canvas.height() as f64;

        Ok(WeatherApp {
            canvas,
            context: context.clone(),
            renderer: Renderer::new(context, width, height),
            landscape: Landscape::new(width, height),
            celestial: CelestialBodies::new(),
            animation: AnimationSystem::new(),
            weather_data: None,
            last_update: Date::now(),
        })
    }

    #[wasm_bindgen]
    pub fn update(&mut self) -> Result<(), JsValue> {
        let now = Date::now();
        let delta_time = (now - self.last_update) / 1000.0; // Convert to seconds
        self.last_update = now;

        // Update celestial bodies based on current time
        self.celestial.update(now);
        
        // Update animations with weather context
        self.animation.update_with_weather(delta_time, self.weather_data.as_ref());

        // Render the scene
        self.render()?;

        Ok(())
    }

    #[wasm_bindgen]
    pub fn set_weather_data(&mut self, weather_json: &str) -> Result<(), JsValue> {
        match serde_json::from_str::<WeatherData>(weather_json) {
            Ok(weather) => {
                console_log!("Weather data updated: {} in {}", weather.description, weather.location);
                self.weather_data = Some(weather);
                Ok(())
            }
            Err(e) => {
                console_log!("Failed to parse weather data: {}", e);
                Err(JsValue::from_str(&format!("Failed to parse weather data: {}", e)))
            }
        }
    }

    fn render(&mut self) -> Result<(), JsValue> {
        // Clear canvas
        self.renderer.clear();

        // Render sky gradient based on time of day
        self.renderer.render_sky(&self.celestial);

        // Render landscape
        self.renderer.render_landscape(&self.landscape);

        // Render celestial bodies (sun/moon)
        self.renderer.render_celestial(&self.celestial);

        // Render weather effects if available
        if let Some(ref weather) = self.weather_data {
            self.renderer.render_weather(weather, &mut self.animation);
        }

        // Render animated elements (birds, etc.)
        self.renderer.render_animations(&self.animation);

        Ok(())
    }

    #[wasm_bindgen]
    pub fn resize(&mut self, width: u32, height: u32) {
        self.canvas.set_width(width);
        self.canvas.set_height(height);
        self.renderer.resize(width as f64, height as f64);
        self.landscape.resize(width as f64, height as f64);
    }
}

#[wasm_bindgen(start)]
pub fn main() {
    console_log!("Rusty Rain Weather App initialized!");
}

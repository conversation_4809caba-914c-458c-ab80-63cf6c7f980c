use wasm_bindgen::prelude::*;
use web_sys::CanvasRenderingContext2d;
use crate::{WeatherData, Landscape, CelestialBodies, AnimationSystem};
use crate::weather::WeatherType;


pub struct Renderer {
    context: CanvasRenderingContext2d,
    width: f64,
    height: f64,
}

impl Renderer {
    pub fn new(context: CanvasRenderingContext2d, width: f64, height: f64) -> Self {
        Self {
            context,
            width,
            height,
        }
    }

    pub fn clear(&self) {
        self.context.clear_rect(0.0, 0.0, self.width, self.height);
    }

    pub fn resize(&mut self, width: f64, height: f64) {
        self.width = width;
        self.height = height;
    }

    pub fn render_sky(&self, celestial: &CelestialBodies) {
        let (top_color, _bottom_color) = celestial.get_sky_colors();

        // Use solid color for now (can add gradients later)
        self.context.set_fill_style(&JsValue::from_str(&top_color));
        self.context.fill_rect(0.0, 0.0, self.width, self.height * 0.7);
    }

    pub fn render_landscape(&self, landscape: &Landscape) {
        // Render ground
        self.context.set_fill_style(&JsValue::from_str("#2d5016"));
        self.context.fill_rect(0.0, self.height * 0.7, self.width, self.height * 0.3);

        // Render hills/mountains
        self.context.begin_path();
        self.context.move_to(0.0, self.height * 0.7);
        
        for (i, height) in landscape.terrain_heights.iter().enumerate() {
            let x = (i as f64 / landscape.terrain_heights.len() as f64) * self.width;
            let y = self.height * 0.7 - height * self.height * 0.3;
            self.context.line_to(x, y);
        }
        
        self.context.line_to(self.width, self.height * 0.7);
        self.context.close_path();
        self.context.set_fill_style(&JsValue::from_str("#1a3d0a"));
        self.context.fill();
    }

    pub fn render_celestial(&self, celestial: &CelestialBodies) {
        if celestial.is_day {
            self.render_sun(celestial);
        } else {
            self.render_moon(celestial);
        }
    }

    fn render_sun(&self, celestial: &CelestialBodies) {
        let (x, y) = celestial.get_sun_position(self.width, self.height * 0.7);

        // Sun disc (simplified - no glow for now)
        self.context.set_fill_style(&JsValue::from_str("#ffff00"));
        self.context.begin_path();
        self.context.arc(x, y, 25.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    fn render_moon(&self, celestial: &CelestialBodies) {
        let (x, y) = celestial.get_moon_position(self.width, self.height * 0.7);

        // Moon disc (simplified - no glow for now)
        self.context.set_fill_style(&JsValue::from_str("#f0f0f0"));
        self.context.begin_path();
        self.context.arc(x, y, 20.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    pub fn render_weather(&self, weather: &WeatherData, animation: &mut AnimationSystem) {
        let weather_type = weather.get_weather_type();
        
        // Render clouds
        if weather_type.has_clouds() {
            self.render_clouds(weather, animation);
        }

        // Render precipitation
        if weather_type.has_precipitation() {
            match weather_type {
                WeatherType::Rain | WeatherType::RainShowers | WeatherType::Drizzle => {
                    self.render_rain(weather, animation);
                }
                WeatherType::Snow | WeatherType::SnowShowers => {
                    self.render_snow(weather, animation);
                }
                WeatherType::Thunderstorm => {
                    self.render_rain(weather, animation);
                    if animation.should_show_lightning() {
                        self.render_lightning();
                    }
                }
                _ => {}
            }
        }

        // Render fog
        if matches!(weather_type, WeatherType::Fog) {
            self.render_fog(weather);
        }
    }

    fn render_clouds(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let cloud_density = weather.get_cloud_density();
        let num_clouds = (cloud_density * 6.0) as usize + 2;

        // Simple but realistic clouds for now
        let cloud_color = "rgba(220, 220, 220, 0.8)";

        for i in 0..num_clouds {
            let x = (i as f64 * self.width * 1.2 / num_clouds as f64 + animation.cloud_offset) % (self.width + 150.0) - 75.0;
            let y = 40.0 + (i as f64 * 20.0) % 60.0;
            let size = 50.0 + (i as f64 * 25.0) % 40.0;

            self.render_realistic_cloud(x, y, size, cloud_color, i);
        }
    }

    fn render_realistic_cloud(&self, x: f64, y: f64, size: f64, color: &str, seed: usize) {
        self.context.set_fill_style(&JsValue::from_str(color));

        // Create irregular, natural cloud shapes using multiple overlapping circles
        let puffs = 8 + (seed % 4); // 8-11 puffs per cloud for natural variation

        for i in 0..puffs {
            let angle = (i as f64 / puffs as f64) * 2.0 * std::f64::consts::PI;
            let radius_variation = 0.7 + ((seed + i) % 6) as f64 * 0.1; // 0.7 to 1.2
            let distance_variation = 0.3 + ((seed * 2 + i) % 5) as f64 * 0.1; // 0.3 to 0.7

            let puff_x = x + angle.cos() * size * distance_variation;
            let puff_y = y + angle.sin() * size * 0.3 * distance_variation; // Flatter clouds
            let puff_size = size * radius_variation * (0.4 + ((seed + i * 3) % 4) as f64 * 0.15);

            self.context.begin_path();
            self.context.arc(puff_x, puff_y, puff_size, 0.0, 2.0 * std::f64::consts::PI).unwrap();
            self.context.fill();
        }

        // Add central mass for density
        self.context.begin_path();
        self.context.arc(x, y, size * 0.6, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    fn render_rain(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let intensity = weather.get_precipitation_intensity();
        let num_drops = (intensity * 200.0) as usize;
        
        self.context.set_stroke_style(&JsValue::from_str("rgba(100, 150, 255, 0.6)"));
        self.context.set_line_width(1.0);
        
        for i in 0..num_drops {
            let x = (i as f64 * 7.0 + animation.rain_offset) % self.width;
            let y = (i as f64 * 11.0 + animation.rain_offset * 2.0) % self.height;
            
            self.context.begin_path();
            self.context.move_to(x, y);
            self.context.line_to(x - 2.0, y + 10.0);
            self.context.stroke();
        }
    }

    fn render_snow(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let intensity = weather.get_precipitation_intensity();
        let num_flakes = (intensity * 100.0) as usize;
        
        self.context.set_fill_style(&JsValue::from_str("rgba(255, 255, 255, 0.8)"));
        
        for i in 0..num_flakes {
            let x = (i as f64 * 13.0 + animation.snow_offset) % self.width;
            let y = (i as f64 * 17.0 + animation.snow_offset * 0.5) % self.height;
            
            self.context.begin_path();
            self.context.arc(x, y, 2.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
            self.context.fill();
        }
    }

    fn render_lightning(&self) {
        self.context.set_stroke_style(&JsValue::from_str("rgba(255, 255, 255, 0.9)"));
        self.context.set_line_width(3.0);
        
        let x = self.width * 0.3 + (rand::random::<f64>() * self.width * 0.4);
        
        self.context.begin_path();
        self.context.move_to(x, 0.0);
        self.context.line_to(x + 20.0, self.height * 0.3);
        self.context.line_to(x - 10.0, self.height * 0.5);
        self.context.line_to(x + 15.0, self.height * 0.7);
        self.context.stroke();
    }

    fn render_fog(&self, weather: &WeatherData) {
        let opacity = (weather.visibility / 1000.0).min(0.7);
        self.context.set_fill_style(&JsValue::from_str(&format!("rgba(200, 200, 200, {})", 0.7 - opacity)));
        self.context.fill_rect(0.0, self.height * 0.4, self.width, self.height * 0.6);
    }

    pub fn render_animations(&self, animation: &AnimationSystem) {
        // Render background elements first
        self.render_buildings();
        self.render_road();
        self.render_walking_path();

        // Render cars on the road (behind characters)
        for car in &animation.cars {
            self.render_car(car);
        }

        // Render birds
        for bird in &animation.birds {
            self.render_bird(bird.x, bird.y, bird.wing_phase);
        }

        // Render characters
        for character in &animation.characters {
            self.render_character(character);
        }

        // Render chat bubbles
        for bubble in &animation.chat_bubbles {
            if bubble.duration >= 0.0 { // Only show if not delayed
                self.render_chat_bubble(bubble);
            }
        }
    }

    fn render_bird(&self, x: f64, y: f64, wing_phase: f64) {
        self.context.set_stroke_style(&JsValue::from_str("#333"));
        self.context.set_line_width(2.0);

        let wing_offset = (wing_phase * 2.0 * std::f64::consts::PI).sin() * 5.0;

        self.context.begin_path();
        self.context.move_to(x - 8.0, y + wing_offset);
        self.context.line_to(x, y);
        self.context.line_to(x + 8.0, y + wing_offset);
        self.context.stroke();
    }

    fn render_character(&self, character: &crate::animation::Character) {
        let x = character.x;
        let y = character.y;

        // Character body color based on type
        let (body_color, size_modifier) = match character.character_type {
            crate::animation::CharacterType::Person => ("#4a90e2", 1.0),
            crate::animation::CharacterType::Child => ("#ff6b6b", 0.7),
            crate::animation::CharacterType::Elder => ("#8e44ad", 1.0),
            crate::animation::CharacterType::Dog => ("#8b4513", 0.5),
        };

        let size = 30.0 * size_modifier; // Larger characters for foreground perspective

        if matches!(character.character_type, crate::animation::CharacterType::Dog) {
            // Draw dog
            self.render_dog(x, y, character.walk_phase, character.direction);
        } else {
            // Draw person
            self.render_person(x, y, character.walk_phase, character.direction, body_color, size);
        }

        // Chat indicator removed for cleaner visual appearance
    }

    fn render_person(&self, x: f64, y: f64, walk_phase: f64, direction: f64, color: &str, size: f64) {
        // Head
        self.context.set_fill_style(&JsValue::from_str("#ffdbac"));
        self.context.begin_path();
        self.context.arc(x, y - size * 1.5, size * 0.3, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Body
        self.context.set_fill_style(&JsValue::from_str(color));
        self.context.fill_rect(x - size * 0.2, y - size * 1.2, size * 0.4, size * 0.8);

        // Legs with walking animation
        let leg_offset = (walk_phase * 2.0 * std::f64::consts::PI).sin() * 5.0;
        self.context.set_stroke_style(&JsValue::from_str("#333"));
        self.context.set_line_width(3.0);

        // Left leg
        self.context.begin_path();
        self.context.move_to(x - size * 0.1, y - size * 0.4);
        self.context.line_to(x - size * 0.1 + leg_offset * direction, y);
        self.context.stroke();

        // Right leg
        self.context.begin_path();
        self.context.move_to(x + size * 0.1, y - size * 0.4);
        self.context.line_to(x + size * 0.1 - leg_offset * direction, y);
        self.context.stroke();

        // Arms
        let arm_offset = (walk_phase * 2.0 * std::f64::consts::PI).sin() * 3.0;
        self.context.set_line_width(2.0);

        // Left arm
        self.context.begin_path();
        self.context.move_to(x - size * 0.2, y - size * 1.0);
        self.context.line_to(x - size * 0.3 - arm_offset * direction, y - size * 0.7);
        self.context.stroke();

        // Right arm
        self.context.begin_path();
        self.context.move_to(x + size * 0.2, y - size * 1.0);
        self.context.line_to(x + size * 0.3 + arm_offset * direction, y - size * 0.7);
        self.context.stroke();
    }

    fn render_dog(&self, x: f64, y: f64, walk_phase: f64, direction: f64) {
        let size = 10.0;

        // Body
        self.context.set_fill_style(&JsValue::from_str("#8b4513"));
        self.context.fill_rect(x - size * 0.8, y - size * 0.6, size * 1.6, size * 0.4);

        // Head
        self.context.begin_path();
        self.context.arc(x + direction * size * 0.8, y - size * 0.4, size * 0.3, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Legs with walking animation
        let leg_offset = (walk_phase * 4.0 * std::f64::consts::PI).sin() * 2.0;
        self.context.set_stroke_style(&JsValue::from_str("#654321"));
        self.context.set_line_width(2.0);

        // Front legs
        self.context.begin_path();
        self.context.move_to(x + size * 0.4, y - size * 0.2);
        self.context.line_to(x + size * 0.4 + leg_offset, y);
        self.context.stroke();

        // Back legs
        self.context.begin_path();
        self.context.move_to(x - size * 0.4, y - size * 0.2);
        self.context.line_to(x - size * 0.4 - leg_offset, y);
        self.context.stroke();

        // Tail
        let tail_wag = (walk_phase * 6.0 * std::f64::consts::PI).sin() * 10.0;
        self.context.begin_path();
        self.context.move_to(x - direction * size * 0.8, y - size * 0.4);
        self.context.line_to(x - direction * size * 1.2, y - size * 0.6 + tail_wag);
        self.context.stroke();
    }

    fn render_chat_bubble(&self, bubble: &crate::animation::ChatBubble) {
        let x = bubble.x;
        let y = bubble.y;
        let text = &bubble.message;

        // Measure text to size bubble
        self.context.set_font("12px Arial");
        let text_width = self.context.measure_text(text).unwrap().width();
        let bubble_width = text_width + 20.0;
        let bubble_height = 25.0;

        // Bubble colors based on type and weather context
        let alpha = ((bubble.max_duration - bubble.duration) / bubble.max_duration).min(1.0).max(0.0);
        let (bg_color, border_color, text_color) = match bubble.bubble_type {
            crate::animation::BubbleType::Normal => {
                // Weather-appropriate bubble colors
                if text.contains("storm") || text.contains("scary") || text.contains("dangerous") {
                    ("rgba(255, 200, 200, {})", "rgba(200, 0, 0, {})", "rgba(100, 0, 0, {})")
                } else if text.contains("snow") || text.contains("cold") {
                    ("rgba(200, 220, 255, {})", "rgba(100, 150, 255, {})", "rgba(0, 50, 150, {})")
                } else if text.contains("rain") || text.contains("wet") || text.contains("umbrella") {
                    ("rgba(200, 200, 255, {})", "rgba(100, 100, 200, {})", "rgba(50, 50, 150, {})")
                } else if text.contains("sunny") || text.contains("sunshine") || text.contains("beautiful") {
                    ("rgba(255, 255, 200, {})", "rgba(255, 200, 0, {})", "rgba(150, 100, 0, {})")
                } else {
                    ("rgba(255, 255, 255, {})", "rgba(0, 0, 0, {})", "rgba(0, 0, 0, {})")
                }
            },
            crate::animation::BubbleType::Angry => {
                ("rgba(255, 150, 150, {})", "rgba(255, 0, 0, {})", "rgba(150, 0, 0, {})")
            },
            crate::animation::BubbleType::Fighting => {
                ("rgba(255, 100, 100, {})", "rgba(200, 0, 0, {})", "rgba(100, 0, 0, {})")
            },
        };

        self.context.set_fill_style(&JsValue::from_str(&bg_color.replace("{}", &(alpha * 0.9).to_string())));
        self.context.set_stroke_style(&JsValue::from_str(&border_color.replace("{}", &(alpha * 0.8).to_string())));
        self.context.set_line_width(1.0);

        // Bubble shape
        self.context.begin_path();
        self.context.round_rect(
            x - bubble_width / 2.0,
            y - bubble_height,
            bubble_width,
            bubble_height
        ).unwrap();
        self.context.fill();
        self.context.stroke();

        // Bubble pointer
        self.context.begin_path();
        self.context.move_to(x - 5.0, y - 5.0);
        self.context.line_to(x, y);
        self.context.line_to(x + 5.0, y - 5.0);
        self.context.close_path();
        self.context.fill();
        self.context.stroke();

        // Text
        self.context.set_fill_style(&JsValue::from_str(&text_color.replace("{}", &alpha.to_string())));
        self.context.set_text_align("center");
        self.context.fill_text(text, x, y - bubble_height / 2.0 + 4.0).unwrap();
    }

    fn render_car(&self, car: &crate::animation::Car) {
        let x = car.x;
        let y = car.y;

        // Car color
        let color = match car.color {
            crate::animation::CarColor::Red => "#ff4444",
            crate::animation::CarColor::Blue => "#4444ff",
            crate::animation::CarColor::White => "#ffffff",
            crate::animation::CarColor::Black => "#333333",
            crate::animation::CarColor::Silver => "#cccccc",
            crate::animation::CarColor::Green => "#44ff44",
        };

        // Car size based on type - much larger vehicles for proper scale
        let (width, height) = match car.car_type {
            crate::animation::CarType::Sedan => (125.0, 50.0),
            crate::animation::CarType::SUV => (140.0, 60.0),
            crate::animation::CarType::Truck => (175.0, 70.0),
            crate::animation::CarType::Compact => (100.0, 40.0),
        };

        // Car body
        self.context.set_fill_style(&JsValue::from_str(color));
        self.context.fill_rect(x - width / 2.0, y - height / 2.0, width, height);

        // Car outline
        self.context.set_stroke_style(&JsValue::from_str("#000000"));
        self.context.set_line_width(1.0);
        self.context.stroke_rect(x - width / 2.0, y - height / 2.0, width, height);

        // Windows
        self.context.set_fill_style(&JsValue::from_str("#87ceeb"));
        self.context.fill_rect(x - width / 3.0, y - height / 3.0, width / 1.5, height / 2.5);

        // Add simple driver and passengers
        // Driver head
        let driver_x = if car.direction > 0.0 { x - width * 0.2 } else { x + width * 0.2 };
        self.context.set_fill_style(&JsValue::from_str("#ffdbac"));
        self.context.begin_path();
        self.context.arc(driver_x, y - height * 0.1, 6.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Wheels - scaled to match larger vehicles
        self.context.set_fill_style(&JsValue::from_str("#333333"));
        let wheel_radius = 12.0;

        // Front wheel
        self.context.begin_path();
        self.context.arc(
            x + width / 3.0 * car.direction,
            y + height / 2.0 - 2.0,
            wheel_radius,
            0.0,
            2.0 * std::f64::consts::PI
        ).unwrap();
        self.context.fill();

        // Rear wheel
        self.context.begin_path();
        self.context.arc(
            x - width / 3.0 * car.direction,
            y + height / 2.0 - 2.0,
            wheel_radius,
            0.0,
            2.0 * std::f64::consts::PI
        ).unwrap();
        self.context.fill();
    }

    fn render_road(&self) {
        // Simple horizontal road - half height, no perspective
        self.context.set_fill_style(&JsValue::from_str("#404040"));
        self.context.fill_rect(0.0, 420.0, self.width, 40.0); // Half height: 40px instead of 80px

        // Road markings - simple dashed center line
        self.context.set_stroke_style(&JsValue::from_str("#ffff00"));
        self.context.set_line_width(2.0);
        self.context.begin_path();

        let dash_length = 20.0;
        let gap_length = 15.0;
        let y = 440.0; // Center of road
        let mut x = 0.0;

        while x < self.width {
            self.context.move_to(x, y);
            self.context.line_to(x + dash_length, y);
            x += dash_length + gap_length;
        }
        self.context.stroke();

        // Simple road edges - straight horizontal lines
        self.context.set_stroke_style(&JsValue::from_str("#ffffff"));
        self.context.set_line_width(1.0);
        self.context.begin_path();
        self.context.move_to(0.0, 420.0); // Top edge
        self.context.line_to(self.width, 420.0);
        self.context.move_to(0.0, 460.0); // Bottom edge
        self.context.line_to(self.width, 460.0);
        self.context.stroke();
    }

    fn render_buildings(&self) {
        // Background buildings for depth
        let building_colors = ["#8B4513", "#A0522D", "#CD853F", "#DEB887", "#F4A460"];

        for i in 0..8 {
            let x = (i as f64 * self.width / 8.0) + (i as f64 * 10.0) % 30.0;
            let width = 60.0 + (i as f64 * 20.0) % 40.0;
            let height = 80.0 + (i as f64 * 30.0) % 60.0;
            let y = 400.0 - height;

            // Building body
            let color = building_colors[i % building_colors.len()];
            self.context.set_fill_style(&JsValue::from_str(color));
            self.context.fill_rect(x, y, width, height);

            // Building outline
            self.context.set_stroke_style(&JsValue::from_str("#333333"));
            self.context.set_line_width(1.0);
            self.context.stroke_rect(x, y, width, height);

            // Windows
            self.context.set_fill_style(&JsValue::from_str("#87CEEB"));
            let window_rows = ((height / 25.0) as usize).max(1);
            let window_cols = ((width / 20.0) as usize).max(1);

            for row in 0..window_rows {
                for col in 0..window_cols {
                    let window_x = x + 5.0 + col as f64 * (width - 10.0) / window_cols as f64;
                    let window_y = y + 5.0 + row as f64 * (height - 10.0) / window_rows as f64;
                    self.context.fill_rect(window_x, window_y, 8.0, 12.0);
                }
            }
        }
    }



    fn render_walking_path(&self) {
        // Create a wider wobbly walking path for people
        self.context.set_fill_style(&JsValue::from_str("#8B7355"));

        // Create wobbly path using sine wave - make it wider by drawing filled area
        let path_y_base = 500.0; // Below the road
        let wobble_amplitude = 15.0;
        let wobble_frequency = 0.01;
        let path_width = 20.0; // Much wider path

        self.context.begin_path();

        // Draw top edge of path
        let mut x = 0.0;
        self.context.move_to(0.0, path_y_base + (0.0 * wobble_frequency).sin() * wobble_amplitude - path_width/2.0);
        while x < self.width {
            let y = path_y_base + (x * wobble_frequency).sin() * wobble_amplitude - path_width/2.0;
            self.context.line_to(x, y);
            x += 5.0;
        }

        // Draw bottom edge of path (reverse direction)
        x = self.width;
        while x > 0.0 {
            let y = path_y_base + (x * wobble_frequency).sin() * wobble_amplitude + path_width/2.0;
            self.context.line_to(x, y);
            x -= 5.0;
        }

        self.context.close_path();
        self.context.fill();

        // Add some flowers along the path
        let flower_colors = ["#FF69B4", "#FFD700", "#FF6347", "#9370DB", "#32CD32"];

        for i in 0..12 {
            let x = (i as f64 * self.width / 12.0) + ((i * 17) % 30) as f64;
            let path_y = path_y_base + (x * wobble_frequency).sin() * wobble_amplitude;
            let flower_y = path_y + ((i % 2) as f64 * 20.0 - 10.0) + 15.0; // Offset from path
            let color = flower_colors[i % flower_colors.len()];

            self.render_flower(x, flower_y, color);
        }
    }

    fn render_flower(&self, x: f64, y: f64, color: &str) {
        // Flower center
        self.context.set_fill_style(&JsValue::from_str("#FFD700"));
        self.context.begin_path();
        self.context.arc(x, y, 3.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Flower petals
        self.context.set_fill_style(&JsValue::from_str(color));
        for i in 0..5 {
            let angle = (i as f64 * 2.0 * std::f64::consts::PI / 5.0);
            let petal_x = x + angle.cos() * 6.0;
            let petal_y = y + angle.sin() * 6.0;

            self.context.begin_path();
            self.context.arc(petal_x, petal_y, 4.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
            self.context.fill();
        }

        // Small stem
        self.context.set_stroke_style(&JsValue::from_str("#228B22"));
        self.context.set_line_width(2.0);
        self.context.begin_path();
        self.context.move_to(x, y + 3.0);
        self.context.line_to(x, y + 12.0);
        self.context.stroke();
    }
}

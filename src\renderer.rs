use wasm_bindgen::prelude::*;
use web_sys::CanvasRenderingContext2d;
use crate::{WeatherData, Landscape, CelestialBodies, AnimationSystem};
use crate::weather::WeatherType;


pub struct Renderer {
    context: CanvasRenderingContext2d,
    width: f64,
    height: f64,
}

impl Renderer {
    pub fn new(context: CanvasRenderingContext2d, width: f64, height: f64) -> Self {
        Self {
            context,
            width,
            height,
        }
    }

    pub fn clear(&self) {
        self.context.clear_rect(0.0, 0.0, self.width, self.height);
    }

    pub fn resize(&mut self, width: f64, height: f64) {
        self.width = width;
        self.height = height;
    }

    pub fn render_sky(&self, celestial: &CelestialBodies) {
        let (top_color, _bottom_color) = celestial.get_sky_colors();

        // Use solid color for now (can add gradients later)
        self.context.set_fill_style(&JsValue::from_str(&top_color));
        self.context.fill_rect(0.0, 0.0, self.width, self.height * 0.7);
    }

    pub fn render_landscape(&self, landscape: &Landscape) {
        // Render ground
        self.context.set_fill_style(&JsValue::from_str("#2d5016"));
        self.context.fill_rect(0.0, self.height * 0.7, self.width, self.height * 0.3);

        // Render hills/mountains
        self.context.begin_path();
        self.context.move_to(0.0, self.height * 0.7);
        
        for (i, height) in landscape.terrain_heights.iter().enumerate() {
            let x = (i as f64 / landscape.terrain_heights.len() as f64) * self.width;
            let y = self.height * 0.7 - height * self.height * 0.3;
            self.context.line_to(x, y);
        }
        
        self.context.line_to(self.width, self.height * 0.7);
        self.context.close_path();
        self.context.set_fill_style(&JsValue::from_str("#1a3d0a"));
        self.context.fill();
    }

    pub fn render_celestial(&self, celestial: &CelestialBodies) {
        if celestial.is_day {
            self.render_sun(celestial);
        } else {
            self.render_moon(celestial);
        }
    }

    fn render_sun(&self, celestial: &CelestialBodies) {
        let (x, y) = celestial.get_sun_position(self.width, self.height * 0.7);

        // Sun disc (simplified - no glow for now)
        self.context.set_fill_style(&JsValue::from_str("#ffff00"));
        self.context.begin_path();
        self.context.arc(x, y, 25.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    fn render_moon(&self, celestial: &CelestialBodies) {
        let (x, y) = celestial.get_moon_position(self.width, self.height * 0.7);

        // Moon disc (simplified - no glow for now)
        self.context.set_fill_style(&JsValue::from_str("#f0f0f0"));
        self.context.begin_path();
        self.context.arc(x, y, 20.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    pub fn render_weather(&self, weather: &WeatherData, animation: &mut AnimationSystem) {
        let weather_type = weather.get_weather_type();
        
        // Render clouds
        if weather_type.has_clouds() {
            self.render_clouds(weather, animation);
        }

        // Render precipitation
        if weather_type.has_precipitation() {
            match weather_type {
                WeatherType::Rain | WeatherType::RainShowers | WeatherType::Drizzle => {
                    self.render_rain(weather, animation);
                }
                WeatherType::Snow | WeatherType::SnowShowers => {
                    self.render_snow(weather, animation);
                }
                WeatherType::Thunderstorm => {
                    self.render_rain(weather, animation);
                    if animation.should_show_lightning() {
                        self.render_lightning();
                    }
                }
                _ => {}
            }
        }

        // Render fog
        if matches!(weather_type, WeatherType::Fog) {
            self.render_fog(weather);
        }
    }

    fn render_clouds(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let cloud_density = weather.get_cloud_density();
        let num_clouds = (cloud_density * 8.0) as usize + 2;
        
        self.context.set_fill_style(&JsValue::from_str("rgba(200, 200, 200, 0.7)"));
        
        for i in 0..num_clouds {
            let x = (i as f64 * self.width / num_clouds as f64 + animation.cloud_offset) % (self.width + 100.0) - 50.0;
            let y = 50.0 + (i as f64 * 30.0) % 100.0;
            
            self.render_cloud(x, y, 60.0 + (i as f64 * 20.0) % 40.0);
        }
    }

    fn render_cloud(&self, x: f64, y: f64, size: f64) {
        self.context.begin_path();
        self.context.arc(x, y, size * 0.5, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.arc(x + size * 0.3, y, size * 0.4, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.arc(x - size * 0.3, y, size * 0.4, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.arc(x, y - size * 0.2, size * 0.3, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    fn render_rain(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let intensity = weather.get_precipitation_intensity();
        let num_drops = (intensity * 200.0) as usize;
        
        self.context.set_stroke_style(&JsValue::from_str("rgba(100, 150, 255, 0.6)"));
        self.context.set_line_width(1.0);
        
        for i in 0..num_drops {
            let x = (i as f64 * 7.0 + animation.rain_offset) % self.width;
            let y = (i as f64 * 11.0 + animation.rain_offset * 2.0) % self.height;
            
            self.context.begin_path();
            self.context.move_to(x, y);
            self.context.line_to(x - 2.0, y + 10.0);
            self.context.stroke();
        }
    }

    fn render_snow(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let intensity = weather.get_precipitation_intensity();
        let num_flakes = (intensity * 100.0) as usize;
        
        self.context.set_fill_style(&JsValue::from_str("rgba(255, 255, 255, 0.8)"));
        
        for i in 0..num_flakes {
            let x = (i as f64 * 13.0 + animation.snow_offset) % self.width;
            let y = (i as f64 * 17.0 + animation.snow_offset * 0.5) % self.height;
            
            self.context.begin_path();
            self.context.arc(x, y, 2.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
            self.context.fill();
        }
    }

    fn render_lightning(&self) {
        self.context.set_stroke_style(&JsValue::from_str("rgba(255, 255, 255, 0.9)"));
        self.context.set_line_width(3.0);
        
        let x = self.width * 0.3 + (rand::random::<f64>() * self.width * 0.4);
        
        self.context.begin_path();
        self.context.move_to(x, 0.0);
        self.context.line_to(x + 20.0, self.height * 0.3);
        self.context.line_to(x - 10.0, self.height * 0.5);
        self.context.line_to(x + 15.0, self.height * 0.7);
        self.context.stroke();
    }

    fn render_fog(&self, weather: &WeatherData) {
        let opacity = (weather.visibility / 1000.0).min(0.7);
        self.context.set_fill_style(&JsValue::from_str(&format!("rgba(200, 200, 200, {})", 0.7 - opacity)));
        self.context.fill_rect(0.0, self.height * 0.4, self.width, self.height * 0.6);
    }

    pub fn render_animations(&self, animation: &AnimationSystem) {
        // Render cars first (behind characters)
        for car in &animation.cars {
            self.render_car(car);
        }

        // Render birds
        for bird in &animation.birds {
            self.render_bird(bird.x, bird.y, bird.wing_phase);
        }

        // Render characters
        for character in &animation.characters {
            self.render_character(character);
        }

        // Render chat bubbles
        for bubble in &animation.chat_bubbles {
            if bubble.duration >= 0.0 { // Only show if not delayed
                self.render_chat_bubble(bubble);
            }
        }
    }

    fn render_bird(&self, x: f64, y: f64, wing_phase: f64) {
        self.context.set_stroke_style(&JsValue::from_str("#333"));
        self.context.set_line_width(2.0);

        let wing_offset = (wing_phase * 2.0 * std::f64::consts::PI).sin() * 5.0;

        self.context.begin_path();
        self.context.move_to(x - 8.0, y + wing_offset);
        self.context.line_to(x, y);
        self.context.line_to(x + 8.0, y + wing_offset);
        self.context.stroke();
    }

    fn render_character(&self, character: &crate::animation::Character) {
        let x = character.x;
        let y = character.y;

        // Character body color based on type
        let (body_color, size_modifier) = match character.character_type {
            crate::animation::CharacterType::Person => ("#4a90e2", 1.0),
            crate::animation::CharacterType::Child => ("#ff6b6b", 0.7),
            crate::animation::CharacterType::Elder => ("#8e44ad", 1.0),
            crate::animation::CharacterType::Dog => ("#8b4513", 0.5),
        };

        let size = 20.0 * size_modifier;

        if matches!(character.character_type, crate::animation::CharacterType::Dog) {
            // Draw dog
            self.render_dog(x, y, character.walk_phase, character.direction);
        } else {
            // Draw person
            self.render_person(x, y, character.walk_phase, character.direction, body_color, size);
        }

        // Add chat indicator if chatting
        if character.is_chatting {
            self.context.set_fill_style(&JsValue::from_str("rgba(255, 255, 0, 0.3)"));
            self.context.begin_path();
            self.context.arc(x, y - size, size * 0.8, 0.0, 2.0 * std::f64::consts::PI).unwrap();
            self.context.fill();
        }
    }

    fn render_person(&self, x: f64, y: f64, walk_phase: f64, direction: f64, color: &str, size: f64) {
        // Head
        self.context.set_fill_style(&JsValue::from_str("#ffdbac"));
        self.context.begin_path();
        self.context.arc(x, y - size * 1.5, size * 0.3, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Body
        self.context.set_fill_style(&JsValue::from_str(color));
        self.context.fill_rect(x - size * 0.2, y - size * 1.2, size * 0.4, size * 0.8);

        // Legs with walking animation
        let leg_offset = (walk_phase * 2.0 * std::f64::consts::PI).sin() * 5.0;
        self.context.set_stroke_style(&JsValue::from_str("#333"));
        self.context.set_line_width(3.0);

        // Left leg
        self.context.begin_path();
        self.context.move_to(x - size * 0.1, y - size * 0.4);
        self.context.line_to(x - size * 0.1 + leg_offset * direction, y);
        self.context.stroke();

        // Right leg
        self.context.begin_path();
        self.context.move_to(x + size * 0.1, y - size * 0.4);
        self.context.line_to(x + size * 0.1 - leg_offset * direction, y);
        self.context.stroke();

        // Arms
        let arm_offset = (walk_phase * 2.0 * std::f64::consts::PI).sin() * 3.0;
        self.context.set_line_width(2.0);

        // Left arm
        self.context.begin_path();
        self.context.move_to(x - size * 0.2, y - size * 1.0);
        self.context.line_to(x - size * 0.3 - arm_offset * direction, y - size * 0.7);
        self.context.stroke();

        // Right arm
        self.context.begin_path();
        self.context.move_to(x + size * 0.2, y - size * 1.0);
        self.context.line_to(x + size * 0.3 + arm_offset * direction, y - size * 0.7);
        self.context.stroke();
    }

    fn render_dog(&self, x: f64, y: f64, walk_phase: f64, direction: f64) {
        let size = 10.0;

        // Body
        self.context.set_fill_style(&JsValue::from_str("#8b4513"));
        self.context.fill_rect(x - size * 0.8, y - size * 0.6, size * 1.6, size * 0.4);

        // Head
        self.context.begin_path();
        self.context.arc(x + direction * size * 0.8, y - size * 0.4, size * 0.3, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Legs with walking animation
        let leg_offset = (walk_phase * 4.0 * std::f64::consts::PI).sin() * 2.0;
        self.context.set_stroke_style(&JsValue::from_str("#654321"));
        self.context.set_line_width(2.0);

        // Front legs
        self.context.begin_path();
        self.context.move_to(x + size * 0.4, y - size * 0.2);
        self.context.line_to(x + size * 0.4 + leg_offset, y);
        self.context.stroke();

        // Back legs
        self.context.begin_path();
        self.context.move_to(x - size * 0.4, y - size * 0.2);
        self.context.line_to(x - size * 0.4 - leg_offset, y);
        self.context.stroke();

        // Tail
        let tail_wag = (walk_phase * 6.0 * std::f64::consts::PI).sin() * 10.0;
        self.context.begin_path();
        self.context.move_to(x - direction * size * 0.8, y - size * 0.4);
        self.context.line_to(x - direction * size * 1.2, y - size * 0.6 + tail_wag);
        self.context.stroke();
    }

    fn render_chat_bubble(&self, bubble: &crate::animation::ChatBubble) {
        let x = bubble.x;
        let y = bubble.y;
        let text = &bubble.message;

        // Measure text to size bubble
        self.context.set_font("12px Arial");
        let text_width = self.context.measure_text(text).unwrap().width();
        let bubble_width = text_width + 20.0;
        let bubble_height = 25.0;

        // Bubble colors based on type and weather context
        let alpha = ((bubble.max_duration - bubble.duration) / bubble.max_duration).min(1.0).max(0.0);
        let (bg_color, border_color, text_color) = match bubble.bubble_type {
            crate::animation::BubbleType::Normal => {
                // Weather-appropriate bubble colors
                if text.contains("storm") || text.contains("scary") || text.contains("dangerous") {
                    ("rgba(255, 200, 200, {})", "rgba(200, 0, 0, {})", "rgba(100, 0, 0, {})")
                } else if text.contains("snow") || text.contains("cold") {
                    ("rgba(200, 220, 255, {})", "rgba(100, 150, 255, {})", "rgba(0, 50, 150, {})")
                } else if text.contains("rain") || text.contains("wet") || text.contains("umbrella") {
                    ("rgba(200, 200, 255, {})", "rgba(100, 100, 200, {})", "rgba(50, 50, 150, {})")
                } else if text.contains("sunny") || text.contains("sunshine") || text.contains("beautiful") {
                    ("rgba(255, 255, 200, {})", "rgba(255, 200, 0, {})", "rgba(150, 100, 0, {})")
                } else {
                    ("rgba(255, 255, 255, {})", "rgba(0, 0, 0, {})", "rgba(0, 0, 0, {})")
                }
            },
            crate::animation::BubbleType::Angry => {
                ("rgba(255, 150, 150, {})", "rgba(255, 0, 0, {})", "rgba(150, 0, 0, {})")
            },
            crate::animation::BubbleType::Fighting => {
                ("rgba(255, 100, 100, {})", "rgba(200, 0, 0, {})", "rgba(100, 0, 0, {})")
            },
        };

        self.context.set_fill_style(&JsValue::from_str(&bg_color.replace("{}", &(alpha * 0.9).to_string())));
        self.context.set_stroke_style(&JsValue::from_str(&border_color.replace("{}", &(alpha * 0.8).to_string())));
        self.context.set_line_width(1.0);

        // Bubble shape
        self.context.begin_path();
        self.context.round_rect(
            x - bubble_width / 2.0,
            y - bubble_height,
            bubble_width,
            bubble_height
        ).unwrap();
        self.context.fill();
        self.context.stroke();

        // Bubble pointer
        self.context.begin_path();
        self.context.move_to(x - 5.0, y - 5.0);
        self.context.line_to(x, y);
        self.context.line_to(x + 5.0, y - 5.0);
        self.context.close_path();
        self.context.fill();
        self.context.stroke();

        // Text
        self.context.set_fill_style(&JsValue::from_str(&text_color.replace("{}", &alpha.to_string())));
        self.context.set_text_align("center");
        self.context.fill_text(text, x, y - bubble_height / 2.0 + 4.0).unwrap();
    }

    fn render_car(&self, car: &crate::animation::Car) {
        let x = car.x;
        let y = car.y;

        // Car color
        let color = match car.color {
            crate::animation::CarColor::Red => "#ff4444",
            crate::animation::CarColor::Blue => "#4444ff",
            crate::animation::CarColor::White => "#ffffff",
            crate::animation::CarColor::Black => "#333333",
            crate::animation::CarColor::Silver => "#cccccc",
            crate::animation::CarColor::Green => "#44ff44",
        };

        // Car size based on type
        let (width, height) = match car.car_type {
            crate::animation::CarType::Sedan => (60.0, 25.0),
            crate::animation::CarType::SUV => (65.0, 30.0),
            crate::animation::CarType::Truck => (80.0, 35.0),
            crate::animation::CarType::Compact => (45.0, 20.0),
        };

        // Car body
        self.context.set_fill_style(&JsValue::from_str(color));
        self.context.fill_rect(x - width / 2.0, y - height / 2.0, width, height);

        // Car outline
        self.context.set_stroke_style(&JsValue::from_str("#000000"));
        self.context.set_line_width(1.0);
        self.context.stroke_rect(x - width / 2.0, y - height / 2.0, width, height);

        // Windows
        self.context.set_fill_style(&JsValue::from_str("#87ceeb"));
        self.context.fill_rect(x - width / 3.0, y - height / 3.0, width / 1.5, height / 2.5);

        // Wheels
        self.context.set_fill_style(&JsValue::from_str("#333333"));
        let wheel_radius = 6.0;

        // Front wheel
        self.context.begin_path();
        self.context.arc(
            x + width / 3.0 * car.direction,
            y + height / 2.0 - 2.0,
            wheel_radius,
            0.0,
            2.0 * std::f64::consts::PI
        ).unwrap();
        self.context.fill();

        // Rear wheel
        self.context.begin_path();
        self.context.arc(
            x - width / 3.0 * car.direction,
            y + height / 2.0 - 2.0,
            wheel_radius,
            0.0,
            2.0 * std::f64::consts::PI
        ).unwrap();
        self.context.fill();
    }
}

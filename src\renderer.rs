use wasm_bindgen::prelude::*;
use web_sys::CanvasRenderingContext2d;
use crate::{WeatherData, Landscape, CelestialBodies, AnimationSystem};
use crate::weather::WeatherType;
use rand::Rng;

pub struct Renderer {
    context: CanvasRenderingContext2d,
    width: f64,
    height: f64,
}

impl Renderer {
    pub fn new(context: CanvasRenderingContext2d, width: f64, height: f64) -> Self {
        Self {
            context,
            width,
            height,
        }
    }

    pub fn clear(&self) {
        self.context.clear_rect(0.0, 0.0, self.width, self.height);
    }

    pub fn resize(&mut self, width: f64, height: f64) {
        self.width = width;
        self.height = height;
    }

    pub fn render_sky(&self, celestial: &CelestialBodies) {
        let gradient = self.context
            .create_linear_gradient(0.0, 0.0, 0.0, self.height * 0.7)
            .unwrap();

        let (top_color, bottom_color) = celestial.get_sky_colors();
        
        gradient.add_color_stop(0.0, &top_color).unwrap();
        gradient.add_color_stop(1.0, &bottom_color).unwrap();

        self.context.set_fill_style(&gradient);
        self.context.fill_rect(0.0, 0.0, self.width, self.height * 0.7);
    }

    pub fn render_landscape(&self, landscape: &Landscape) {
        // Render ground
        self.context.set_fill_style(&JsValue::from_str("#2d5016"));
        self.context.fill_rect(0.0, self.height * 0.7, self.width, self.height * 0.3);

        // Render hills/mountains
        self.context.begin_path();
        self.context.move_to(0.0, self.height * 0.7);
        
        for (i, height) in landscape.terrain_heights.iter().enumerate() {
            let x = (i as f64 / landscape.terrain_heights.len() as f64) * self.width;
            let y = self.height * 0.7 - height * self.height * 0.3;
            self.context.line_to(x, y);
        }
        
        self.context.line_to(self.width, self.height * 0.7);
        self.context.close_path();
        self.context.set_fill_style(&JsValue::from_str("#1a3d0a"));
        self.context.fill();
    }

    pub fn render_celestial(&self, celestial: &CelestialBodies) {
        if celestial.is_day {
            self.render_sun(celestial);
        } else {
            self.render_moon(celestial);
        }
    }

    fn render_sun(&self, celestial: &CelestialBodies) {
        let (x, y) = celestial.get_sun_position(self.width, self.height * 0.7);
        
        // Sun glow
        let gradient = self.context
            .create_radial_gradient(x, y, 0.0, x, y, 40.0)
            .unwrap();
        gradient.add_color_stop(0.0, "rgba(255, 255, 0, 0.8)").unwrap();
        gradient.add_color_stop(0.5, "rgba(255, 200, 0, 0.4)").unwrap();
        gradient.add_color_stop(1.0, "rgba(255, 200, 0, 0.0)").unwrap();

        self.context.set_fill_style(&gradient);
        self.context.begin_path();
        self.context.arc(x, y, 40.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Sun disc
        self.context.set_fill_style(&JsValue::from_str("#ffff00"));
        self.context.begin_path();
        self.context.arc(x, y, 20.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    fn render_moon(&self, celestial: &CelestialBodies) {
        let (x, y) = celestial.get_moon_position(self.width, self.height * 0.7);
        
        // Moon glow
        let gradient = self.context
            .create_radial_gradient(x, y, 0.0, x, y, 30.0)
            .unwrap();
        gradient.add_color_stop(0.0, "rgba(220, 220, 255, 0.6)").unwrap();
        gradient.add_color_stop(1.0, "rgba(220, 220, 255, 0.0)").unwrap();

        self.context.set_fill_style(&gradient);
        self.context.begin_path();
        self.context.arc(x, y, 30.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();

        // Moon disc
        self.context.set_fill_style(&JsValue::from_str("#f0f0f0"));
        self.context.begin_path();
        self.context.arc(x, y, 15.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    pub fn render_weather(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let weather_type = weather.get_weather_type();
        
        // Render clouds
        if weather_type.has_clouds() {
            self.render_clouds(weather, animation);
        }

        // Render precipitation
        if weather_type.has_precipitation() {
            match weather_type {
                WeatherType::Rain | WeatherType::RainShowers | WeatherType::Drizzle => {
                    self.render_rain(weather, animation);
                }
                WeatherType::Snow | WeatherType::SnowShowers => {
                    self.render_snow(weather, animation);
                }
                WeatherType::Thunderstorm => {
                    self.render_rain(weather, animation);
                    if animation.should_show_lightning() {
                        self.render_lightning();
                    }
                }
                _ => {}
            }
        }

        // Render fog
        if matches!(weather_type, WeatherType::Fog) {
            self.render_fog(weather);
        }
    }

    fn render_clouds(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let cloud_density = weather.get_cloud_density();
        let num_clouds = (cloud_density * 8.0) as usize + 2;
        
        self.context.set_fill_style(&JsValue::from_str("rgba(200, 200, 200, 0.7)"));
        
        for i in 0..num_clouds {
            let x = (i as f64 * self.width / num_clouds as f64 + animation.cloud_offset) % (self.width + 100.0) - 50.0;
            let y = 50.0 + (i as f64 * 30.0) % 100.0;
            
            self.render_cloud(x, y, 60.0 + (i as f64 * 20.0) % 40.0);
        }
    }

    fn render_cloud(&self, x: f64, y: f64, size: f64) {
        self.context.begin_path();
        self.context.arc(x, y, size * 0.5, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.arc(x + size * 0.3, y, size * 0.4, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.arc(x - size * 0.3, y, size * 0.4, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.arc(x, y - size * 0.2, size * 0.3, 0.0, 2.0 * std::f64::consts::PI).unwrap();
        self.context.fill();
    }

    fn render_rain(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let intensity = weather.get_precipitation_intensity();
        let num_drops = (intensity * 200.0) as usize;
        
        self.context.set_stroke_style(&JsValue::from_str("rgba(100, 150, 255, 0.6)"));
        self.context.set_line_width(1.0);
        
        for i in 0..num_drops {
            let x = (i as f64 * 7.0 + animation.rain_offset) % self.width;
            let y = (i as f64 * 11.0 + animation.rain_offset * 2.0) % self.height;
            
            self.context.begin_path();
            self.context.move_to(x, y);
            self.context.line_to(x - 2.0, y + 10.0);
            self.context.stroke();
        }
    }

    fn render_snow(&self, weather: &WeatherData, animation: &AnimationSystem) {
        let intensity = weather.get_precipitation_intensity();
        let num_flakes = (intensity * 100.0) as usize;
        
        self.context.set_fill_style(&JsValue::from_str("rgba(255, 255, 255, 0.8)"));
        
        for i in 0..num_flakes {
            let x = (i as f64 * 13.0 + animation.snow_offset) % self.width;
            let y = (i as f64 * 17.0 + animation.snow_offset * 0.5) % self.height;
            
            self.context.begin_path();
            self.context.arc(x, y, 2.0, 0.0, 2.0 * std::f64::consts::PI).unwrap();
            self.context.fill();
        }
    }

    fn render_lightning(&self) {
        self.context.set_stroke_style(&JsValue::from_str("rgba(255, 255, 255, 0.9)"));
        self.context.set_line_width(3.0);
        
        let x = self.width * 0.3 + (rand::random::<f64>() * self.width * 0.4);
        
        self.context.begin_path();
        self.context.move_to(x, 0.0);
        self.context.line_to(x + 20.0, self.height * 0.3);
        self.context.line_to(x - 10.0, self.height * 0.5);
        self.context.line_to(x + 15.0, self.height * 0.7);
        self.context.stroke();
    }

    fn render_fog(&self, weather: &WeatherData) {
        let opacity = (weather.visibility / 1000.0).min(0.7);
        self.context.set_fill_style(&JsValue::from_str(&format!("rgba(200, 200, 200, {})", 0.7 - opacity)));
        self.context.fill_rect(0.0, self.height * 0.4, self.width, self.height * 0.6);
    }

    pub fn render_animations(&self, animation: &AnimationSystem) {
        // Render birds
        for bird in &animation.birds {
            self.render_bird(bird.x, bird.y, bird.wing_phase);
        }
    }

    fn render_bird(&self, x: f64, y: f64, wing_phase: f64) {
        self.context.set_stroke_style(&JsValue::from_str("#333"));
        self.context.set_line_width(2.0);
        
        let wing_offset = (wing_phase * 2.0 * std::f64::consts::PI).sin() * 5.0;
        
        self.context.begin_path();
        self.context.move_to(x - 8.0, y + wing_offset);
        self.context.line_to(x, y);
        self.context.line_to(x + 8.0, y + wing_offset);
        self.context.stroke();
    }
}

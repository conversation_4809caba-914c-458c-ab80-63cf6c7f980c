use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WeatherData {
    pub location: String,
    pub temperature: f64,
    pub humidity: f64,
    pub pressure: f64,
    pub wind_speed: f64,
    pub wind_direction: f64,
    pub visibility: f64,
    pub cloud_cover: f64,
    pub precipitation: f64,
    pub weather_code: u32,
    pub description: String,
    pub is_day: bool,
    pub sunrise: Option<u64>,
    pub sunset: Option<u64>,
}

impl WeatherData {
    pub fn get_weather_type(&self) -> WeatherType {
        match self.weather_code {
            0 => WeatherType::Clear,
            1..=3 => WeatherType::PartlyCloudy,
            45 | 48 => WeatherType::Fog,
            51..=57 => WeatherType::Drizzle,
            61..=67 => WeatherType::Rain,
            71..=77 => WeatherType::Snow,
            80..=82 => WeatherType::RainShowers,
            85..=86 => WeatherType::SnowShowers,
            95..=99 => WeatherType::Thunderstorm,
            _ => WeatherType::Clear,
        }
    }

    pub fn get_cloud_density(&self) -> f64 {
        self.cloud_cover / 100.0
    }

    pub fn get_precipitation_intensity(&self) -> f64 {
        self.precipitation / 10.0 // Normalize to 0-1 range
    }

    pub fn get_wind_strength(&self) -> f64 {
        (self.wind_speed / 50.0).min(1.0) // Normalize to 0-1 range, cap at 50 km/h
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum WeatherType {
    Clear,
    PartlyCloudy,
    Fog,
    Drizzle,
    Rain,
    Snow,
    RainShowers,
    SnowShowers,
    Thunderstorm,
}

impl WeatherType {
    pub fn has_precipitation(&self) -> bool {
        matches!(
            self,
            WeatherType::Drizzle
                | WeatherType::Rain
                | WeatherType::Snow
                | WeatherType::RainShowers
                | WeatherType::SnowShowers
                | WeatherType::Thunderstorm
        )
    }

    pub fn has_clouds(&self) -> bool {
        !matches!(self, WeatherType::Clear)
    }

    pub fn is_stormy(&self) -> bool {
        matches!(self, WeatherType::Thunderstorm)
    }
}

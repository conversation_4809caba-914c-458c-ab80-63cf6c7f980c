{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 16630486044567369389, "deps": [[1988483478007900009, "unicode_ident", false, 9352588111964915200], [3060637413840920116, "proc_macro2", false, 4051905311661989900], [17990358020177143287, "quote", false, 10131327620748597160]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-9deb77e41a607292\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
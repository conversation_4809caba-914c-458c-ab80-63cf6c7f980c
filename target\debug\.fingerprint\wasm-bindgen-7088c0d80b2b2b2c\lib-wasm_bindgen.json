{"rustc": 1842507548689473721, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 17865070097449935239, "path": 2869143884344618175, "deps": [[2828590642173593838, "cfg_if", false, 1218731334873354671], [3722963349756955755, "once_cell", false, 15204227220872754618], [6946689283190175495, "build_script_build", false, 16143033865029405502], [7858942147296547339, "rustversion", false, 7241523957370537597], [11382113702854245495, "wasm_bindgen_macro", false, 5100259676298161971]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-7088c0d80b2b2b2c\\dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
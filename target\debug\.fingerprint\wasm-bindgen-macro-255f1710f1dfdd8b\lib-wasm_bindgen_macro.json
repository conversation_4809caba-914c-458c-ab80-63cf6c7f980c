{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 1300506145316312068, "path": 9830646261406001082, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 4736184765583074963], [17990358020177143287, "quote", false, 10131327620748597160]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-255f1710f1dfdd8b\\dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
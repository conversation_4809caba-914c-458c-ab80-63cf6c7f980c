{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 1300506145316312068, "path": 12991177566143412103, "deps": [[3060637413840920116, "proc_macro2", false, 4051905311661989900], [4974441333307933176, "syn", false, 15042624007395956053], [14299170049494554845, "wasm_bindgen_shared", false, 16403856776350205333], [14372503175394433084, "wasm_bindgen_backend", false, 4608680741929809103], [17990358020177143287, "quote", false, 10131327620748597160]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-macro-support-40a237076dcccddb\\dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 8958406094080315647, "profile": 1300506145316312068, "path": 15128862843910285148, "deps": [[1988483478007900009, "unicode_ident", false, 9352588111964915200], [14299170049494554845, "build_script_build", false, 13398932968223892303]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wasm-bindgen-shared-98116b1b6bc8932b\\dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
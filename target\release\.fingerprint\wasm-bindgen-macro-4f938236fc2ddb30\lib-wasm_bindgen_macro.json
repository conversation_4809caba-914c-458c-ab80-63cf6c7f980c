{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 6875603382767429092, "profile": 2554159132106277380, "path": 9830646261406001082, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 9402719671103173761], [17990358020177143287, "quote", false, 14682674654187063660]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wasm-bindgen-macro-4f938236fc2ddb30\\dep-lib-wasm_bindgen_macro", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
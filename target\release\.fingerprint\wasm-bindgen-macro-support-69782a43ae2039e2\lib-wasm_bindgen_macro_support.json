{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 2554159132106277380, "path": 12991177566143412103, "deps": [[3060637413840920116, "proc_macro2", false, 11611705843154361789], [4974441333307933176, "syn", false, 5933869653494872195], [14299170049494554845, "wasm_bindgen_shared", false, 6044186084053006524], [14372503175394433084, "wasm_bindgen_backend", false, 8019189001079549312], [17990358020177143287, "quote", false, 14682674654187063660]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wasm-bindgen-macro-support-69782a43ae2039e2\\dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 8958406094080315647, "profile": 2554159132106277380, "path": 15128862843910285148, "deps": [[1988483478007900009, "unicode_ident", false, 3090222587142496273], [14299170049494554845, "build_script_build", false, 13672862066804593423]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wasm-bindgen-shared-73da18ded5b50e76\\dep-lib-wasm_bindgen_shared", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 1842507548689473721, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 13212958829184517904, "path": 2869143884344618175, "deps": [[2828590642173593838, "cfg_if", false, 18309505692335664172], [3722963349756955755, "once_cell", false, 992019038139411429], [6946689283190175495, "build_script_build", false, 2064137554126345023], [7858942147296547339, "rustversion", false, 7404741876449241598], [11382113702854245495, "wasm_bindgen_macro", false, 10733912542168953369]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown\\release\\.fingerprint\\wasm-bindgen-4aea7bae439225b3\\dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}